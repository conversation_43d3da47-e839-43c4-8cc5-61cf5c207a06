# 任务1.5：创建内存存储提供者

## 📋 任务概述

**任务ID**: Task-1.5  
**任务名称**: 创建内存存储提供者  
**所属阶段**: 阶段1 - 基础设施层重构  
**预计时间**: 2小时  
**依赖任务**: Task-1.4 (创建本地存储提供者)  
**后续任务**: Task-1.6 (实现存储工厂)  

## 🎯 任务目标

创建基于内存的临时存储提供者，主要用于单元测试、缓存和临时数据存储，实现IStorageProvider接口的完整功能。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-1.4 已完成
- [ ] IStorageProvider接口已定义
- [ ] LocalStorageProvider已实现

### 文件检查
- [ ] `src/infrastructure/interfaces/IStorageProvider.ts` 存在
- [ ] `src/infrastructure/types/StorageConfig.ts` 存在
- [ ] `src/infrastructure/types/StorageResult.ts` 存在
- [ ] `src/infrastructure/providers/LocalStorageProvider.ts` 存在

### 状态检查
```bash
# 检查是否已存在MemoryStorageProvider
if [ -f "src/infrastructure/providers/MemoryStorageProvider.ts" ]; then
  echo "⚠️  MemoryStorageProvider已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建MemoryStorageProvider"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建MemoryStorageProvider类
**文件**: `src/infrastructure/providers/MemoryStorageProvider.ts`

**主要功能**:
- 实现IStorageProvider接口
- 基于内存Map存储
- 支持TTL（生存时间）
- 支持自动过期清理
- 支持大小限制
- 完整的错误处理

**核心方法**:
```typescript
export class MemoryStorageProvider extends BaseStorageProvider {
  private storage: Map<string, MemoryStorageItem> = new Map();
  private maxSize: number = 10 * 1024 * 1024; // 10MB
  private ttl: number = 3600000; // 1小时
  private cleanupTimer?: NodeJS.Timeout;
  
  async initialize(config: IStorageConfig): Promise<void>
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>>
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>
  async delete(key: string): Promise<StorageResult<void>>
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>
  // ... 其他接口方法
}
```

### 步骤2：定义内存存储项结构
**存储项接口**:
```typescript
interface MemoryStorageItem {
  data: any;
  metadata: {
    size: number;
    lastModified: Date;
    etag: string;
    contentType: string;
    expiresAt?: Date;
  };
}
```

### 步骤3：实现TTL和自动清理
**TTL功能**:
```typescript
private isExpired(item: MemoryStorageItem): boolean
private cleanupExpired(): void
private startCleanupTimer(): void
```

### 步骤4：实现大小管理
**大小管理**:
```typescript
private calculateSize(data: any): number
private getTotalSize(): number
private checkSizeLimit(newDataSize: number): boolean
```

### 步骤5：实现高级功能
**高级功能**:
```typescript
async clear(): Promise<void>
getItemCount(): number
has(key: string): boolean
```

### 步骤6：更新StorageProviderFactory
**文件**: `src/infrastructure/providers/StorageProviderFactory.ts`

添加MemoryStorageProvider支持:
```typescript
import { MemoryStorageProvider } from './MemoryStorageProvider';

// 在create方法中添加
case StorageType.MEMORY_STORAGE:
  provider = new MemoryStorageProvider();
  break;
```

### 步骤7：创建单元测试
**文件**: `src/infrastructure/providers/__tests__/MemoryStorageProvider.test.ts`

**测试用例**:
- 初始化测试
- CRUD操作测试
- 批量操作测试
- TTL功能测试
- 自动清理测试
- 大小限制测试
- 错误处理测试

### 步骤8：创建配置工厂方法
**文件**: `src/infrastructure/types/StorageConfig.ts`

添加内存存储配置创建方法:
```typescript
static createMemoryStorageConfig(config: {
  name: string;
  maxSize?: number;
  ttl?: number;
}): IMemoryStorageConfig
```

## ✅ 验收标准

### 功能验收
- [ ] MemoryStorageProvider类创建成功
- [ ] 实现了IStorageProvider接口的所有方法
- [ ] 支持内存数据存储
- [ ] 支持TTL功能
- [ ] 支持自动过期清理
- [ ] 支持大小限制检查
- [ ] 错误处理机制完善

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 代码符合项目编码规范
- [ ] 无ESLint警告或错误

### 性能验收
- [ ] 初始化时间 < 100ms
- [ ] 基础操作响应时间 < 10ms
- [ ] 内存使用合理
- [ ] 清理机制高效

### 功能验收
- [ ] 适合单元测试使用
- [ ] 适合缓存场景
- [ ] 支持高并发访问
- [ ] 内存泄漏防护

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.5执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/providers/LocalStorageProvider.ts" ]; then
  echo "❌ 依赖任务Task-1.4未完成，请先执行Task-1.4"
  exit 1
fi

# 检查是否已存在实现
if [ -f "src/infrastructure/providers/MemoryStorageProvider.ts" ]; then
  echo "⚠️  MemoryStorageProvider.ts已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 可以开始执行任务1.5"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.5执行结果..."

# 检查文件是否创建
if [ ! -f "src/infrastructure/providers/MemoryStorageProvider.ts" ]; then
  echo "❌ MemoryStorageProvider.ts文件未创建"
  exit 1
fi

# 检查TypeScript编译
echo "检查TypeScript编译..."
npx tsc --noEmit --project tsconfig.json
if [ $? -ne 0 ]; then
  echo "❌ TypeScript编译失败"
  exit 1
fi

# 检查测试文件
if [ ! -f "src/infrastructure/providers/__tests__/MemoryStorageProvider.test.ts" ]; then
  echo "⚠️  测试文件未创建"
fi

echo "✅ 任务1.5执行验证通过"
```

## 📝 完成确认

### 任务完成检查清单
- [ ] MemoryStorageProvider类实现完成
- [ ] 所有接口方法实现
- [ ] 内存存储功能实现
- [ ] TTL功能实现
- [ ] 自动清理机制实现
- [ ] 大小限制检查实现
- [ ] 错误处理完善
- [ ] 单元测试编写
- [ ] StorageProviderFactory更新
- [ ] 配置工厂方法添加
- [ ] 文档更新

### 测试验证
```bash
# 运行单元测试
npm test -- MemoryStorageProvider.test.ts

# 运行集成测试
npm run test:integration

# 检查代码质量
npm run lint
npm run type-check
```

### 性能验证
```bash
# 运行性能测试
npm run test:performance -- MemoryStorageProvider

# 内存泄漏测试
npm run test:memory-leak -- MemoryStorageProvider
```

## 🔗 相关链接

- [IStorageProvider接口文档](../interfaces/01-infrastructure-interfaces.md)
- [StorageConfig类型定义](../../types/StorageConfig.md)
- [任务1.4：创建本地存储提供者](./task-1.4-local-storage-provider.md)
- [任务1.6：实现存储工厂](./task-1.6-storage-factory.md)

## 📋 注意事项

### 内存管理
- 及时清理过期数据
- 监控内存使用量
- 防止内存泄漏
- 合理设置大小限制

### TTL机制
- 支持可配置的TTL
- 高效的过期检查
- 自动清理机制
- 避免频繁清理影响性能

### 并发安全
- 虽然JavaScript是单线程，但要考虑异步操作
- 确保数据一致性
- 避免竞态条件

### 测试友好
- 提供清理方法
- 支持测试隔离
- 快速重置状态
- 模拟各种场景

### 使用场景
- 单元测试数据存储
- 临时缓存
- 会话数据存储
- 开发环境快速原型
