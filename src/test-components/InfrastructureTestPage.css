.infrastructure-test-page {
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.infrastructure-test-page .header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.infrastructure-test-page .header h1 {
  margin: 0;
  font-size: 2.5em;
  font-weight: 300;
}

.infrastructure-test-page .header p {
  margin: 10px 0 0 0;
  opacity: 0.9;
  font-size: 1.1em;
}

.infrastructure-test-page .main-content {
  max-width: 1400px;
  margin: 0 auto;
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.infrastructure-test-page .test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.infrastructure-test-page .test-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.infrastructure-test-page .test-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.infrastructure-test-page .test-card h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.3em;
  display: flex;
  align-items: center;
  gap: 10px;
}

.infrastructure-test-page .test-card .icon {
  font-size: 1.5em;
}

.infrastructure-test-page .button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 15px 0;
}

.infrastructure-test-page .btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.infrastructure-test-page .btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.infrastructure-test-page .btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.infrastructure-test-page .btn.success {
  background: #28a745;
}

.infrastructure-test-page .btn.success:hover {
  background: #218838;
}

.infrastructure-test-page .btn.danger {
  background: #dc3545;
}

.infrastructure-test-page .btn.danger:hover {
  background: #c82333;
}

.infrastructure-test-page .btn.warning {
  background: #ffc107;
  color: #212529;
}

.infrastructure-test-page .btn.warning:hover {
  background: #e0a800;
}

.infrastructure-test-page .status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: auto;
}

.infrastructure-test-page .status-success {
  background: #28a745;
}

.infrastructure-test-page .status-error {
  background: #dc3545;
}

.infrastructure-test-page .status-warning {
  background: #ffc107;
}

.infrastructure-test-page .status-info {
  background: #17a2b8;
}

.infrastructure-test-page .log-container {
  background: #2d3748;
  color: #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.infrastructure-test-page .log-entry {
  margin: 2px 0;
  padding: 2px 0;
}

.infrastructure-test-page .log-success {
  color: #68d391;
}

.infrastructure-test-page .log-error {
  color: #fc8181;
}

.infrastructure-test-page .log-warning {
  color: #f6e05e;
}

.infrastructure-test-page .log-info {
  color: #63b3ed;
}

.infrastructure-test-page .stats-bar {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.infrastructure-test-page .stat-item {
  text-align: center;
}

.infrastructure-test-page .stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.infrastructure-test-page .stat-label {
  color: #6c757d;
  font-size: 0.9em;
}

.infrastructure-test-page .progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin: 10px 0;
}

.infrastructure-test-page .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.3s ease;
}

.infrastructure-test-page .config-panel {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
}

.infrastructure-test-page .config-panel h4 {
  margin: 0 0 15px 0;
  color: #495057;
}

.infrastructure-test-page .config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.infrastructure-test-page .config-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.infrastructure-test-page .config-item label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.infrastructure-test-page .config-item input,
.infrastructure-test-page .config-item select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.infrastructure-test-page .config-item input:focus,
.infrastructure-test-page .config-item select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .infrastructure-test-page {
    padding: 10px;
  }
  
  .infrastructure-test-page .main-content {
    padding: 20px;
  }
  
  .infrastructure-test-page .test-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .infrastructure-test-page .stats-bar {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .infrastructure-test-page .header h1 {
    font-size: 2em;
  }
  
  .infrastructure-test-page .test-card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .infrastructure-test-page .stats-bar {
    grid-template-columns: 1fr;
  }
  
  .infrastructure-test-page .button-group {
    flex-direction: column;
  }
  
  .infrastructure-test-page .btn {
    width: 100%;
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.infrastructure-test-page .test-card {
  animation: fadeIn 0.5s ease-out;
}

.infrastructure-test-page .log-entry {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
.infrastructure-test-page .log-container::-webkit-scrollbar {
  width: 8px;
}

.infrastructure-test-page .log-container::-webkit-scrollbar-track {
  background: #4a5568;
  border-radius: 4px;
}

.infrastructure-test-page .log-container::-webkit-scrollbar-thumb {
  background: #718096;
  border-radius: 4px;
}

.infrastructure-test-page .log-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
