import React, { useState, useEffect, useRef } from 'react';
import { StorageProviderFactory } from '../infrastructure/providers/StorageProviderFactory';
import { StorageType } from '../infrastructure/enums/StorageType';
import { IStorageProvider } from '../infrastructure/interfaces/IStorageProvider';
import { StorageConfigFactory } from '../infrastructure/types/StorageConfig';

// 日志类型定义
interface LogEntry {
  message: string;
  type: 'info' | 'success' | 'error' | 'warning';
  timestamp: string;
  provider?: string;
}

// 测试统计
interface TestStats {
  total: number;
  tested: number;
  passed: number;
  failed: number;
}

// 提供者状态
interface ProviderStatus {
  status: 'idle' | 'testing' | 'success' | 'error';
  stats?: any;
}

// 日志容器组件
const LogContainer: React.FC<{ logs: LogEntry[] }> = ({ logs }) => {
  const logRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (logRef.current) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs]);

  return (
    <div className="log-container" ref={logRef}>
      {logs.map((log, index) => (
        <div key={index} className={`log-entry log-${log.type}`}>
          [{log.timestamp}] {log.message}
        </div>
      ))}
    </div>
  );
};

// 存储提供者测试卡片组件
const ProviderTestCard: React.FC<{
  providerType: StorageType;
  onLog: (message: string, type: LogEntry['type'], provider?: string) => void;
}> = ({ providerType, onLog }) => {
  const [status, setStatus] = useState<ProviderStatus>({ status: 'idle' });
  const [provider, setProvider] = useState<IStorageProvider | null>(null);

  const log = (message: string, type: LogEntry['type'] = 'info') => {
    onLog(message, type, getProviderDisplayName(providerType));
  };

  const getProviderDisplayName = (type: StorageType): string => {
    const names = {
      [StorageType.MEMORY_STORAGE]: '内存存储',
      [StorageType.LOCAL_STORAGE]: '本地存储',
      [StorageType.INDEXED_DB]: '浏览器数据库',
      [StorageType.HUAWEI_OBS]: '华为云OBS',
      [StorageType.MINIO]: 'MinIO'
    };
    return names[type] || type;
  };

  const getProviderIcon = (type: StorageType): string => {
    const icons = {
      [StorageType.MEMORY_STORAGE]: '💾',
      [StorageType.LOCAL_STORAGE]: '🏠',
      [StorageType.INDEXED_DB]: '🗄️',
      [StorageType.HUAWEI_OBS]: '☁️',
      [StorageType.MINIO]: '📦'
    };
    return icons[type] || '📁';
  };

  const initializeProvider = async () => {
    try {
      const providerInstance = StorageProviderFactory.create(providerType);
      
      // 创建适当的配置
      let config;
      switch (providerType) {
        case StorageType.MEMORY_STORAGE:
          config = StorageConfigFactory.createMemoryStorageConfig({
            name: 'test-memory-storage',
            maxSize: 1024 * 1024, // 1MB
            ttl: 3600000 // 1小时
          });
          break;
        case StorageType.LOCAL_STORAGE:
          config = StorageConfigFactory.createLocalStorageConfig({
            name: 'test-local-storage',
            storageKey: 'memorykeeper_test',
            maxSize: 5 * 1024 * 1024 // 5MB
          });
          break;
        case StorageType.INDEXED_DB:
          config = StorageConfigFactory.createIndexedDBConfig({
            name: 'test-indexed-db',
            storageKey: 'memorykeeper_test_idb',
            maxSize: 10 * 1024 * 1024 // 10MB
          });
          break;
        default:
          // 对于云存储，使用模拟配置
          config = {
            type: providerType,
            name: `test-${providerType}`,
            timeout: 10000,
            retryCount: 3
          };
      }

      await providerInstance.initialize(config);
      setProvider(providerInstance);
      log('✅ 提供者初始化成功', 'success');
      return providerInstance;
    } catch (error) {
      log(`❌ 提供者初始化失败: ${error.message}`, 'error');
      throw error;
    }
  };

  const testBasicOperations = async () => {
    setStatus({ status: 'testing' });
    log(`开始测试基础操作...`, 'info');

    try {
      let providerInstance = provider;
      if (!providerInstance) {
        providerInstance = await initializeProvider();
      }

      // 测试连接
      const connected = await providerInstance.testConnection();
      if (connected) {
        log('✅ 连接测试通过', 'success');
      } else {
        log('❌ 连接测试失败', 'error');
        setStatus({ status: 'error' });
        return;
      }

      // 测试写入
      const testData = { 
        message: 'Hello World', 
        timestamp: Date.now(),
        testId: Math.random().toString(36).substr(2, 9)
      };
      const putResult = await providerInstance.put('test-key', testData);
      if (putResult.success) {
        log('✅ 数据写入成功', 'success');
      } else {
        log(`❌ 数据写入失败: ${putResult.error?.message}`, 'error');
        setStatus({ status: 'error' });
        return;
      }

      // 测试读取
      const getResult = await providerInstance.get('test-key');
      if (getResult.success && getResult.data) {
        log('✅ 数据读取成功', 'success');
        log(`📄 读取的数据: ${JSON.stringify(getResult.data).substring(0, 100)}...`, 'info');
      } else {
        log(`❌ 数据读取失败: ${getResult.error?.message}`, 'error');
        setStatus({ status: 'error' });
        return;
      }

      // 测试元数据获取
      try {
        const metadataResult = await providerInstance.getMetadata('test-key');
        if (metadataResult.success) {
          log('✅ 元数据获取成功', 'success');
          log(`📊 元数据: 大小=${metadataResult.data?.size}字节`, 'info');
        }
      } catch (error) {
        log(`⚠️ 元数据获取失败: ${error.message}`, 'warning');
      }

      // 测试列表
      const listResult = await providerInstance.list();
      if (listResult.success) {
        log(`✅ 列表操作成功，找到 ${listResult.data?.length || 0} 个对象`, 'success');
      } else {
        log(`❌ 列表操作失败: ${listResult.error?.message}`, 'error');
        setStatus({ status: 'error' });
        return;
      }

      // 测试删除
      const deleteResult = await providerInstance.delete('test-key');
      if (deleteResult.success) {
        log('✅ 数据删除成功', 'success');
      } else {
        log(`❌ 数据删除失败: ${deleteResult.error?.message}`, 'error');
        setStatus({ status: 'error' });
        return;
      }

      setStatus({ status: 'success' });
      log(`🎉 所有基础操作测试通过`, 'success');

    } catch (error) {
      log(`❌ 测试失败: ${error.message}`, 'error');
      setStatus({ status: 'error' });
    }
  };

  const testBatchOperations = async () => {
    log(`开始测试批量操作...`, 'info');

    try {
      let providerInstance = provider;
      if (!providerInstance) {
        providerInstance = await initializeProvider();
      }

      // 批量写入测试数据
      const batchData: Record<string, any> = {};
      for (let i = 0; i < 5; i++) {
        batchData[`batch-key-${i}`] = { 
          id: i, 
          data: `test data ${i}`,
          timestamp: Date.now()
        };
      }

      // 执行批量写入
      const putBatchResult = await providerInstance.putBatch(batchData);
      if (putBatchResult.success) {
        log('✅ 批量写入操作成功', 'success');
      } else {
        log(`❌ 批量写入失败: ${putBatchResult.error?.message}`, 'error');
        return;
      }

      // 执行批量读取
      const keys = Object.keys(batchData);
      const getBatchResult = await providerInstance.getBatch(keys);
      if (getBatchResult.success) {
        log(`✅ 批量读取操作成功，读取了 ${Object.keys(getBatchResult.data || {}).length} 个对象`, 'success');
      } else {
        log(`❌ 批量读取失败: ${getBatchResult.error?.message}`, 'error');
      }

      // 执行批量删除
      const deleteBatchResult = await providerInstance.deleteBatch(keys);
      if (deleteBatchResult.success) {
        log('✅ 批量删除操作成功', 'success');
      } else {
        log(`❌ 批量删除失败: ${deleteBatchResult.error?.message}`, 'error');
      }

    } catch (error) {
      log(`❌ 批量操作测试失败: ${error.message}`, 'error');
    }
  };

  const getProviderStats = async () => {
    try {
      let providerInstance = provider;
      if (!providerInstance) {
        providerInstance = await initializeProvider();
      }

      const result = await providerInstance.getStats();
      if (result.success) {
        setStatus(prev => ({ ...prev, stats: result.data }));
        log('✅ 统计信息获取成功', 'success');
        log(`📊 统计: ${result.data?.totalObjects}个对象, ${(result.data?.totalSize || 0)}字节`, 'info');
      } else {
        log(`❌ 获取统计信息失败: ${result.error?.message}`, 'error');
      }
    } catch (error) {
      log(`❌ 获取统计信息失败: ${error.message}`, 'error');
    }
  };

  const getStatusIcon = () => {
    switch (status.status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'testing': return '🔄';
      default: return '⚪';
    }
  };

  const getStatusClass = () => {
    switch (status.status) {
      case 'success': return 'status-success';
      case 'error': return 'status-error';
      case 'testing': return 'status-warning';
      default: return 'status-info';
    }
  };

  return (
    <div className="test-card">
      <h3>
        <span className="icon">{getProviderIcon(providerType)}</span>
        {getProviderDisplayName(providerType)}
        <span className={`status-indicator ${getStatusClass()}`}></span>
        {getStatusIcon()}
      </h3>
      
      <div className="button-group">
        <button 
          className="btn" 
          onClick={testBasicOperations}
          disabled={status.status === 'testing'}
        >
          🧪 基础操作测试
        </button>
        <button 
          className="btn" 
          onClick={testBatchOperations}
          disabled={status.status === 'testing'}
        >
          📦 批量操作测试
        </button>
        <button 
          className="btn" 
          onClick={getProviderStats}
          disabled={status.status === 'testing'}
        >
          📊 获取统计信息
        </button>
      </div>

      {status.stats && (
        <div style={{ marginTop: '15px', fontSize: '14px', color: '#6c757d' }}>
          <strong>统计信息:</strong><br/>
          对象数量: {status.stats.totalObjects} | 
          总大小: {(status.stats.totalSize / 1024).toFixed(2)} KB | 
          已用空间: {(status.stats.usedSpace / 1024).toFixed(2)} KB
        </div>
      )}
    </div>
  );
};

// 主测试页面组件
const InfrastructureTestPage: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [stats, setStats] = useState<TestStats>({
    total: 0,
    tested: 0,
    passed: 0,
    failed: 0
  });

  const supportedTypes = [
    StorageType.MEMORY_STORAGE,
    StorageType.LOCAL_STORAGE,
    StorageType.INDEXED_DB,
    StorageType.HUAWEI_OBS,
    StorageType.MINIO
  ];

  useEffect(() => {
    setStats(prev => ({ ...prev, total: supportedTypes.length }));
    addLog('🚀 React基础设施层功能验证页面已加载', 'info');
    addLog(`📋 发现 ${supportedTypes.length} 个存储提供者类型`, 'info');
  }, []);

  const addLog = (message: string, type: LogEntry['type'] = 'info', provider = 'System') => {
    const newLog: LogEntry = {
      message: `[${provider}] ${message}`,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    setLogs(prev => [...prev, newLog]);
  };

  const clearLogs = () => {
    setLogs([]);
    addLog('🧹 日志已清除', 'info');
  };

  const testStorageFactory = () => {
    addLog('🏭 测试存储工厂功能...', 'info');
    
    try {
      const factorySupportedTypes = StorageProviderFactory.getSupportedTypes();
      addLog(`✅ 工厂支持的存储类型: ${factorySupportedTypes.join(', ')}`, 'success');
      
      // 测试创建每种类型的提供者
      factorySupportedTypes.forEach(type => {
        try {
          const provider = StorageProviderFactory.create(type);
          addLog(`✅ 成功创建 ${type} 提供者: ${provider.name}`, 'success');
        } catch (error) {
          addLog(`❌ 创建 ${type} 提供者失败: ${error.message}`, 'error');
        }
      });
      
      addLog('🎉 存储工厂测试完成', 'success');
    } catch (error) {
      addLog(`❌ 存储工厂测试失败: ${error.message}`, 'error');
    }
  };

  const runAllTests = async () => {
    addLog('🎯 开始运行所有存储提供者测试...', 'info');
    addLog('ℹ️ 请手动点击各个提供者的测试按钮进行详细测试', 'info');
  };

  return (
    <div className="infrastructure-test-page">
      <div className="header">
        <h1>🏗️ React基础设施层功能验证</h1>
        <p>验证阶段1开发的所有存储提供者功能和接口</p>
      </div>

      <div className="main-content">
        <div className="stats-bar">
          <div className="stat-item">
            <div className="stat-number">{stats.total}</div>
            <div className="stat-label">存储提供者</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">{stats.tested}</div>
            <div className="stat-label">已测试</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">{stats.passed}</div>
            <div className="stat-label">测试通过</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">{stats.failed}</div>
            <div className="stat-label">测试失败</div>
          </div>
        </div>

        <div className="config-panel">
          <h4>🎛️ 测试控制面板</h4>
          <div className="button-group">
            <button className="btn success" onClick={runAllTests}>
              🚀 运行所有测试
            </button>
            <button className="btn" onClick={testStorageFactory}>
              🏭 测试存储工厂
            </button>
            <button className="btn warning" onClick={clearLogs}>
              🧹 清除日志
            </button>
          </div>
        </div>

        <div className="test-grid">
          {supportedTypes.map((type, index) => (
            <ProviderTestCard 
              key={index} 
              providerType={type} 
              onLog={addLog}
            />
          ))}
        </div>

        <div className="test-card">
          <h3>
            <span className="icon">📋</span>
            测试日志
          </h3>
          <LogContainer logs={logs} />
        </div>
      </div>
    </div>
  );
};

export default InfrastructureTestPage;
