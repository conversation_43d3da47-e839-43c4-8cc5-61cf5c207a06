import { OperationResult } from '../../infrastructure/types/StorageResult';
import { BaseEntity } from '../entities/BaseEntity';

/**
 * 数据操作结果 (使用统一的OperationResult)
 */
export interface DataResult<T> extends OperationResult<T> {}

/**
 * 分页请求参数
 */
export interface PageRequest {
  page: number;
  size: number;
  sort?: SortCriteria[];
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
  first: boolean;
  last: boolean;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * 排序条件
 */
export interface SortCriteria {
  property: string;
  direction: 'ASC' | 'DESC';
}

/**
 * 搜索条件
 */
export interface SearchCriteria<T = any> {
  filters?: FilterCriteria<T>[];
  sort?: SortCriteria[];
  search?: string;
  limit?: number;
  offset?: number;
}

/**
 * 过滤条件
 */
export interface FilterCriteria<T = any> {
  property: keyof T;
  operator: FilterOperator;
  value?: any;
  values?: any[];
}

/**
 * 过滤操作符
 */
export enum FilterOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  LIKE = 'like',
  ILIKE = 'ilike', // 不区分大小写的LIKE
  IN = 'in',
  NOT_IN = 'nin',
  IS_NULL = 'null',
  IS_NOT_NULL = 'not_null',
  BETWEEN = 'between',
  CONTAINS = 'contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with'
}

/**
 * 批量操作结果
 */
export interface BatchResult<T> {
  success: boolean;
  results: DataResult<T>[];
  successCount: number;
  failureCount: number;
  errors: Error[];
  metadata?: Record<string, any>;
}

/**
 * 数据验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * 验证错误
 */
export interface ValidationError {
  field: string;
  message: string;
  value: any;
  code?: string;
}

/**
 * 数据变更事件
 */
export interface DataChangeEvent<T = any> {
  type: DataChangeType;
  entityType: string;
  entityId: string;
  oldData?: T;
  newData?: T;
  timestamp: Date;
  source?: string;
}

/**
 * 数据变更类型
 */
export enum DataChangeType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  RESTORED = 'restored'
}

/**
 * 缓存选项
 */
export interface CacheOptions {
  ttl?: number; // 生存时间（毫秒）
  maxSize?: number; // 最大缓存大小
  strategy?: CacheStrategy;
}

/**
 * 缓存策略
 */
export enum CacheStrategy {
  LRU = 'lru', // 最近最少使用
  LFU = 'lfu', // 最少使用频率
  FIFO = 'fifo', // 先进先出
  TTL = 'ttl' // 基于时间
}

/**
 * 数据同步状态
 */
export interface SyncStatus {
  status: SyncState;
  lastSyncAt?: Date;
  nextSyncAt?: Date;
  version: number;
  conflicts?: SyncConflict[];
}

/**
 * 同步状态枚举
 */
export enum SyncState {
  PENDING = 'pending',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  CONFLICT = 'conflict',
  ERROR = 'error'
}

/**
 * 同步冲突
 */
export interface SyncConflict {
  entityId: string;
  entityType: string;
  localVersion: number;
  remoteVersion: number;
  localData: any;
  remoteData: any;
  conflictFields: string[];
  timestamp: Date;
}

/**
 * 数据工厂类
 */
export class DataResultFactory {
  /**
   * 创建成功结果
   */
  static success<T>(data?: T, metadata?: Record<string, any>): DataResult<T> {
    return {
      success: true,
      data,
      metadata
    };
  }

  /**
   * 创建失败结果
   */
  static failure<T>(error: Error, metadata?: Record<string, any>): DataResult<T> {
    return {
      success: false,
      error,
      metadata
    };
  }

  /**
   * 从Promise创建结果
   */
  static async fromPromise<T>(
    promise: Promise<T>,
    metadata?: Record<string, any>
  ): Promise<DataResult<T>> {
    try {
      const data = await promise;
      return DataResultFactory.success(data, metadata);
    } catch (error) {
      return DataResultFactory.failure(error as Error, metadata);
    }
  }

  /**
   * 创建分页结果
   */
  static createPageResult<T>(
    content: T[],
    totalElements: number,
    page: number,
    size: number
  ): PageResult<T> {
    const totalPages = Math.ceil(totalElements / size);
    return {
      content,
      totalElements,
      totalPages,
      page,
      size,
      first: page === 0,
      last: page >= totalPages - 1,
      hasNext: page < totalPages - 1,
      hasPrevious: page > 0
    };
  }

  /**
   * 创建批量结果
   */
  static createBatchResult<T>(results: DataResult<T>[]): BatchResult<T> {
    const successResults = results.filter(r => r.success);
    const failureResults = results.filter(r => !r.success);

    return {
      success: failureResults.length === 0,
      results,
      successCount: successResults.length,
      failureCount: failureResults.length,
      errors: failureResults.map(r => r.error!).filter(Boolean),
      metadata: {
        totalCount: results.length,
        successRate: successResults.length / results.length
      }
    };
  }
}

/**
 * 搜索工具类
 */
export class SearchUtils {
  /**
   * 构建搜索条件
   */
  static buildCriteria<T>(options: {
    search?: string;
    filters?: Partial<Record<keyof T, any>>;
    sort?: { field: keyof T; direction: 'ASC' | 'DESC' }[];
    limit?: number;
    offset?: number;
  }): SearchCriteria<T> {
    const criteria: SearchCriteria<T> = {};

    if (options.search) {
      criteria.search = options.search;
    }

    if (options.filters) {
      criteria.filters = Object.entries(options.filters)
        .filter(([_, value]) => value !== undefined)
        .map(([property, value]) => ({
          property: property as keyof T,
          operator: FilterOperator.EQUALS,
          value
        }));
    }

    if (options.sort) {
      criteria.sort = options.sort.map(s => ({
        property: s.field as string,
        direction: s.direction
      }));
    }

    if (options.limit) {
      criteria.limit = options.limit;
    }

    if (options.offset) {
      criteria.offset = options.offset;
    }

    return criteria;
  }

  /**
   * 应用过滤条件
   */
  static applyFilters<T>(items: T[], filters: FilterCriteria<T>[]): T[] {
    return items.filter(item => {
      return filters.every(filter => {
        const value = item[filter.property];
        
        switch (filter.operator) {
          case FilterOperator.EQUALS:
            return value === filter.value;
          case FilterOperator.NOT_EQUALS:
            return value !== filter.value;
          case FilterOperator.GREATER_THAN:
            return value > filter.value;
          case FilterOperator.GREATER_THAN_OR_EQUAL:
            return value >= filter.value;
          case FilterOperator.LESS_THAN:
            return value < filter.value;
          case FilterOperator.LESS_THAN_OR_EQUAL:
            return value <= filter.value;
          case FilterOperator.LIKE:
            return String(value).includes(String(filter.value));
          case FilterOperator.ILIKE:
            return String(value).toLowerCase().includes(String(filter.value).toLowerCase());
          case FilterOperator.IN:
            return filter.values?.includes(value);
          case FilterOperator.NOT_IN:
            return !filter.values?.includes(value);
          case FilterOperator.IS_NULL:
            return value == null;
          case FilterOperator.IS_NOT_NULL:
            return value != null;
          case FilterOperator.CONTAINS:
            return Array.isArray(value) && value.includes(filter.value);
          case FilterOperator.STARTS_WITH:
            return String(value).startsWith(String(filter.value));
          case FilterOperator.ENDS_WITH:
            return String(value).endsWith(String(filter.value));
          default:
            return true;
        }
      });
    });
  }

  /**
   * 应用排序
   */
  static applySort<T>(items: T[], sort: SortCriteria[]): T[] {
    if (!sort || sort.length === 0) {
      return items;
    }

    return [...items].sort((a, b) => {
      for (const criteria of sort) {
        const aValue = (a as any)[criteria.property];
        const bValue = (b as any)[criteria.property];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        else if (aValue > bValue) comparison = 1;
        
        if (comparison !== 0) {
          return criteria.direction === 'DESC' ? -comparison : comparison;
        }
      }
      return 0;
    });
  }
}
