import { BaseEntity } from './BaseEntity';

/**
 * 记忆类型枚举
 */
export enum MemoryType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  LINK = 'link',
  NOTE = 'note',
  MIXED = 'mixed'
}

/**
 * 记忆状态枚举
 */
export enum MemoryStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
  DRAFT = 'draft'
}

/**
 * 记忆优先级枚举
 */
export enum MemoryPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * 附件接口
 */
export interface MemoryAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
  path?: string;
  thumbnail?: string;
  metadata?: Record<string, any>;
}

/**
 * 记忆元数据接口
 */
export interface MemoryMetadata {
  people?: string[];
  events?: string[];
  locations?: string[];
  customFields?: Record<string, any>;
  source?: {
    url?: string;
    title?: string;
    domain?: string;
    timestamp?: Date;
  };
  ai?: {
    summary?: string;
    keywords?: string[];
    sentiment?: 'positive' | 'negative' | 'neutral';
    confidence?: number;
  };
}

/**
 * 记忆实体接口
 */
export interface Memory extends BaseEntity {
  // 基本信息
  title: string;
  content: string;
  type: MemoryType;
  status: MemoryStatus;
  priority: MemoryPriority;

  // 分类和标签
  categoryId?: string;
  tags: string[];

  // 附件
  attachments: MemoryAttachment[];

  // 元数据
  metadata: MemoryMetadata;

  // 状态标记
  isPrivate: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  isPinned: boolean;

  // 时间相关
  historicalDate?: Date; // 记忆发生的历史时间
  reminderDate?: Date;   // 提醒时间
  expiryDate?: Date;     // 过期时间

  // 统计信息
  viewCount: number;
  shareCount: number;
  
  // 同步相关
  syncStatus?: 'pending' | 'synced' | 'conflict' | 'error';
  lastSyncAt?: Date;
  syncVersion?: number;
}

/**
 * 记忆搜索条件接口
 */
export interface MemorySearchCriteria {
  // 文本搜索
  query?: string;
  title?: string;
  content?: string;

  // 类型和状态过滤
  types?: MemoryType[];
  statuses?: MemoryStatus[];
  priorities?: MemoryPriority[];

  // 分类和标签过滤
  categoryIds?: string[];
  tags?: string[];
  excludeTags?: string[];

  // 状态过滤
  isPrivate?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
  isPinned?: boolean;

  // 时间范围过滤
  createdAfter?: Date;
  createdBefore?: Date;
  updatedAfter?: Date;
  updatedBefore?: Date;
  historicalAfter?: Date;
  historicalBefore?: Date;

  // 元数据过滤
  people?: string[];
  events?: string[];
  locations?: string[];

  // 排序
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'priority' | 'viewCount' | 'historicalDate';
  sortOrder?: 'asc' | 'desc';

  // 分页
  limit?: number;
  offset?: number;
}

/**
 * 记忆创建请求接口
 */
export interface CreateMemoryRequest {
  title: string;
  content: string;
  type: MemoryType;
  categoryId?: string;
  tags?: string[];
  attachments?: MemoryAttachment[];
  metadata?: Partial<MemoryMetadata>;
  isPrivate?: boolean;
  isFavorite?: boolean;
  priority?: MemoryPriority;
  historicalDate?: Date;
  reminderDate?: Date;
}

/**
 * 记忆更新请求接口
 */
export interface UpdateMemoryRequest {
  title?: string;
  content?: string;
  type?: MemoryType;
  status?: MemoryStatus;
  priority?: MemoryPriority;
  categoryId?: string;
  tags?: string[];
  attachments?: MemoryAttachment[];
  metadata?: Partial<MemoryMetadata>;
  isPrivate?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
  isPinned?: boolean;
  historicalDate?: Date;
  reminderDate?: Date;
  expiryDate?: Date;
}

/**
 * 记忆统计信息接口
 */
export interface MemoryStats {
  totalCount: number;
  privateCount: number;
  favoriteCount: number;
  archivedCount: number;
  pinnedCount: number;
  totalSize: number;
  averageSize: number;
  typeDistribution: Record<MemoryType, number>;
  statusDistribution: Record<MemoryStatus, number>;
  priorityDistribution: Record<MemoryPriority, number>;
  oldestMemory?: Date;
  newestMemory?: Date;
  mostViewedMemory?: {
    id: string;
    title: string;
    viewCount: number;
  };
  recentActivity: {
    created: number;
    updated: number;
    viewed: number;
  };
}

/**
 * 记忆工具类
 */
export class MemoryUtils {
  /**
   * 验证记忆对象
   */
  static validateMemory(memory: any): memory is Memory {
    return (
      memory &&
      typeof memory.title === 'string' &&
      typeof memory.content === 'string' &&
      Object.values(MemoryType).includes(memory.type) &&
      Object.values(MemoryStatus).includes(memory.status) &&
      Array.isArray(memory.tags) &&
      Array.isArray(memory.attachments) &&
      typeof memory.metadata === 'object' &&
      typeof memory.isPrivate === 'boolean' &&
      typeof memory.isFavorite === 'boolean' &&
      typeof memory.isArchived === 'boolean' &&
      typeof memory.isPinned === 'boolean' &&
      typeof memory.viewCount === 'number' &&
      typeof memory.shareCount === 'number'
    );
  }

  /**
   * 创建默认记忆对象
   */
  static createDefaultMemory(data: CreateMemoryRequest): Omit<Memory, keyof BaseEntity> {
    return {
      title: data.title,
      content: data.content,
      type: data.type,
      status: MemoryStatus.ACTIVE,
      priority: data.priority || MemoryPriority.NORMAL,
      categoryId: data.categoryId,
      tags: data.tags || [],
      attachments: data.attachments || [],
      metadata: {
        people: [],
        events: [],
        locations: [],
        customFields: {},
        ...data.metadata
      },
      isPrivate: data.isPrivate || false,
      isFavorite: data.isFavorite || false,
      isArchived: false,
      isPinned: false,
      historicalDate: data.historicalDate,
      reminderDate: data.reminderDate,
      viewCount: 0,
      shareCount: 0
    };
  }

  /**
   * 计算记忆大小（字节）
   */
  static calculateMemorySize(memory: Memory): number {
    const jsonString = JSON.stringify(memory);
    return new Blob([jsonString]).size;
  }

  /**
   * 提取记忆关键词
   */
  static extractKeywords(memory: Memory): string[] {
    const text = `${memory.title} ${memory.content}`.toLowerCase();
    const words = text.match(/\b\w{3,}\b/g) || [];
    const uniqueWords = [...new Set(words)];
    return uniqueWords.slice(0, 20); // 限制关键词数量
  }

  /**
   * 检查记忆是否匹配搜索条件
   */
  static matchesSearchCriteria(memory: Memory, criteria: MemorySearchCriteria): boolean {
    // 文本搜索
    if (criteria.query) {
      const query = criteria.query.toLowerCase();
      const searchText = `${memory.title} ${memory.content}`.toLowerCase();
      if (!searchText.includes(query)) {
        return false;
      }
    }

    // 类型过滤
    if (criteria.types && !criteria.types.includes(memory.type)) {
      return false;
    }

    // 状态过滤
    if (criteria.statuses && !criteria.statuses.includes(memory.status)) {
      return false;
    }

    // 标签过滤
    if (criteria.tags && !criteria.tags.some(tag => memory.tags.includes(tag))) {
      return false;
    }

    // 排除标签
    if (criteria.excludeTags && criteria.excludeTags.some(tag => memory.tags.includes(tag))) {
      return false;
    }

    // 状态标记过滤
    if (criteria.isPrivate !== undefined && memory.isPrivate !== criteria.isPrivate) {
      return false;
    }

    if (criteria.isFavorite !== undefined && memory.isFavorite !== criteria.isFavorite) {
      return false;
    }

    if (criteria.isArchived !== undefined && memory.isArchived !== criteria.isArchived) {
      return false;
    }

    // 时间范围过滤
    if (criteria.createdAfter && memory.createdAt < criteria.createdAfter) {
      return false;
    }

    if (criteria.createdBefore && memory.createdAt > criteria.createdBefore) {
      return false;
    }

    return true;
  }
}
