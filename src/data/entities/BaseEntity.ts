/**
 * 基础实体接口
 * 所有实体都应该继承此接口
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  version?: number;
}

/**
 * 实体工具类
 */
export class EntityUtils {
  /**
   * 生成唯一ID
   */
  static generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建基础实体属性
   */
  static createBaseEntity(id?: string): BaseEntity {
    const now = new Date();
    return {
      id: id || EntityUtils.generateId(),
      createdAt: now,
      updatedAt: now,
      version: 1
    };
  }

  /**
   * 更新实体时间戳
   */
  static updateTimestamp<T extends BaseEntity>(entity: T): T {
    return {
      ...entity,
      updatedAt: new Date(),
      version: (entity.version || 1) + 1
    };
  }

  /**
   * 验证实体基础属性
   */
  static validateBaseEntity(entity: any): entity is BaseEntity {
    return (
      entity &&
      typeof entity.id === 'string' &&
      entity.createdAt instanceof Date &&
      entity.updatedAt instanceof Date &&
      (entity.version === undefined || typeof entity.version === 'number')
    );
  }
}
