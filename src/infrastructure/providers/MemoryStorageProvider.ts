import { IStorageProvider, BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { 
  IStorageConfig, 
  IMemoryStorageConfig,
  StorageConfigFactory 
} from '../types/StorageConfig';
import { 
  StorageResult, 
  StorageResultFactory,
  ObjectMetadata,
  GetOptions,
  PutOptions,
  ListOptions,
  BatchOptions,
  StreamOptions,
  MultipartUploadOptions,
  UploadPartInfo,
  MultipartUploadInfo,
  StorageStats,
  SignedUrlOptions
} from '../types/StorageResult';

/**
 * 内存存储项
 */
interface MemoryStorageItem {
  data: any;
  metadata: {
    size: number;
    lastModified: Date;
    etag: string;
    contentType: string;
    expiresAt?: Date;
  };
}

/**
 * 内存存储提供者
 * 基于内存的临时存储，主要用于测试和缓存
 */
export class MemoryStorageProvider extends BaseStorageProvider {
  private storage: Map<string, MemoryStorageItem> = new Map();
  private maxSize: number = 10 * 1024 * 1024; // 10MB默认限制
  private ttl: number = 3600000; // 1小时默认TTL
  private cleanupTimer?: NodeJS.Timeout;
  
  constructor() {
    super('内存存储提供者', StorageType.MEMORY_STORAGE);
  }
  
  /**
   * 初始化内存存储提供者
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const memoryConfig = config as IMemoryStorageConfig;
      
      // 验证配置
      this.validateConfig(memoryConfig);
      
      this.maxSize = memoryConfig.maxSize || 10 * 1024 * 1024;
      this.ttl = memoryConfig.ttl || 3600000;
      
      this.config = config;
      
      // 清理现有数据
      this.storage.clear();
      
      // 启动定期清理过期数据
      this.startCleanupTimer();
      
      this.initialized = true;
      console.log('内存存储提供者初始化成功');
    } catch (error) {
      console.error('内存存储提供者初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    
    this.storage.clear();
    this.initialized = false;
    console.log('内存存储提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    return this.initialized;
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    this.ensureInitialized();
    
    try {
      const item = this.storage.get(key);
      
      if (!item) {
        return StorageResultFactory.failure(new Error('对象不存在'));
      }
      
      // 检查是否过期
      if (this.isExpired(item)) {
        this.storage.delete(key);
        return StorageResultFactory.failure(new Error('对象已过期'));
      }
      
      // 处理范围请求
      let data = item.data;
      if (options?.range && typeof data === 'string') {
        const { start, end } = options.range;
        data = data.substring(start, end + 1);
      }
      
      return StorageResultFactory.success(data, item.metadata);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 存储对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    try {
      // 检查大小限制
      const dataSize = this.calculateSize(data);
      if (dataSize > this.maxSize) {
        return StorageResultFactory.failure(
          new Error(`数据大小 ${dataSize} 超过限制 ${this.maxSize}`)
        );
      }
      
      // 检查总存储大小
      const currentSize = this.getTotalSize();
      if (currentSize + dataSize > this.maxSize) {
        // 尝试清理过期数据
        this.cleanupExpired();
        
        const newCurrentSize = this.getTotalSize();
        if (newCurrentSize + dataSize > this.maxSize) {
          return StorageResultFactory.failure(
            new Error(`存储空间不足，当前大小: ${newCurrentSize}, 需要: ${dataSize}, 限制: ${this.maxSize}`)
          );
        }
      }
      
      const now = new Date();
      const expiresAt = this.ttl > 0 ? new Date(now.getTime() + this.ttl) : undefined;
      
      const item: MemoryStorageItem = {
        data: data,
        metadata: {
          size: dataSize,
          lastModified: now,
          etag: this.generateETag(data),
          contentType: options?.contentType || 'application/json',
          expiresAt: expiresAt
        }
      };
      
      this.storage.set(key, item);
      
      return StorageResultFactory.success(undefined, {
        etag: item.metadata.etag
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    try {
      const existed = this.storage.has(key);
      this.storage.delete(key);
      
      if (!existed) {
        return StorageResultFactory.failure(new Error('对象不存在'));
      }
      
      return StorageResultFactory.success(undefined);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    this.ensureInitialized();
    
    try {
      // 清理过期数据
      this.cleanupExpired();
      
      let keys = Array.from(this.storage.keys());
      
      // 过滤前缀
      if (prefix) {
        keys = keys.filter(key => key.startsWith(prefix));
      }
      
      // 应用限制
      const maxKeys = options?.maxKeys || 1000;
      keys = keys.slice(0, maxKeys);
      
      return StorageResultFactory.success(keys);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];
    
    for (const key of keys) {
      try {
        const result = await this.get(key);
        if (result.success) {
          results[key] = result.data;
        } else {
          errors.push(`${key}: ${result.error?.message}`);
          if (!options?.continueOnError) {
            break;
          }
        }
      } catch (error) {
        errors.push(`${key}: ${(error as Error).message}`);
        if (!options?.continueOnError) {
          break;
        }
      }
    }
    
    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量获取失败: ${errors.join(', ')}`));
    }
    
    return StorageResultFactory.success(results);
  }
  
  /**
   * 批量存储对象
   */
  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    
    for (const [key, data] of Object.entries(items)) {
      try {
        const result = await this.put(key, data);
        if (!result.success) {
          errors.push(`${key}: ${result.error?.message}`);
          if (!options?.continueOnError) {
            break;
          }
        }
      } catch (error) {
        errors.push(`${key}: ${(error as Error).message}`);
        if (!options?.continueOnError) {
          break;
        }
      }
    }
    
    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量存储失败: ${errors.join(', ')}`));
    }
    
    return StorageResultFactory.success(undefined);
  }
  
  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    
    for (const key of keys) {
      try {
        const result = await this.delete(key);
        if (!result.success) {
          errors.push(`${key}: ${result.error?.message}`);
          if (!options?.continueOnError) {
            break;
          }
        }
      } catch (error) {
        errors.push(`${key}: ${(error as Error).message}`);
        if (!options?.continueOnError) {
          break;
        }
      }
    }
    
    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量删除失败: ${errors.join(', ')}`));
    }
    
    return StorageResultFactory.success(undefined);
  }
  
  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    this.ensureInitialized();
    
    try {
      const item = this.storage.get(key);
      
      if (!item) {
        return StorageResultFactory.failure(new Error('对象不存在'));
      }
      
      // 检查是否过期
      if (this.isExpired(item)) {
        this.storage.delete(key);
        return StorageResultFactory.failure(new Error('对象已过期'));
      }
      
      return StorageResultFactory.success(item.metadata);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  // 以下方法在内存存储中不支持，返回错误
  async getStream(): Promise<ReadableStream> {
    throw new Error('内存存储不支持流操作');
  }
  
  async putStream(): Promise<StorageResult<void>> {
    throw new Error('内存存储不支持流操作');
  }
  
  async initiateMultipartUpload(): Promise<StorageResult<string>> {
    throw new Error('内存存储不支持分块上传');
  }
  
  async uploadPart(): Promise<StorageResult<UploadPartInfo>> {
    throw new Error('内存存储不支持分块上传');
  }
  
  async completeMultipartUpload(): Promise<StorageResult<void>> {
    throw new Error('内存存储不支持分块上传');
  }
  
  async abortMultipartUpload(): Promise<StorageResult<void>> {
    throw new Error('内存存储不支持分块上传');
  }
  
  async listMultipartUploads(): Promise<StorageResult<MultipartUploadInfo[]>> {
    throw new Error('内存存储不支持分块上传');
  }
  
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    // 内存存储返回一个模拟的URL
    return `memory://storage/${key}`;
  }
  
  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageResult<StorageStats>> {
    this.ensureInitialized();
    
    try {
      // 清理过期数据
      this.cleanupExpired();
      
      const totalSize = this.getTotalSize();
      const stats: StorageStats = {
        totalObjects: this.storage.size,
        totalSize: totalSize,
        usedSpace: totalSize,
        availableSpace: this.maxSize - totalSize,
        lastModified: new Date()
      };
      
      return StorageResultFactory.success(stats);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }
  
  /**
   * 清空所有数据
   */
  async clear(): Promise<void> {
    this.storage.clear();
  }
  
  /**
   * 获取存储项数量
   */
  getItemCount(): number {
    return this.storage.size;
  }
  
  /**
   * 检查键是否存在
   */
  has(key: string): boolean {
    const item = this.storage.get(key);
    if (!item) return false;
    
    if (this.isExpired(item)) {
      this.storage.delete(key);
      return false;
    }
    
    return true;
  }
  
  // 私有方法
  private validateConfig(config: IMemoryStorageConfig): void {
    if (config.maxSize && config.maxSize <= 0) {
      throw new Error('maxSize必须大于0');
    }
    
    if (config.ttl && config.ttl <= 0) {
      throw new Error('ttl必须大于0');
    }
  }
  
  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2; // 估算Unicode字符占用
  }
  
  private getTotalSize(): number {
    let totalSize = 0;
    for (const item of this.storage.values()) {
      totalSize += item.metadata.size;
    }
    return totalSize;
  }
  
  private isExpired(item: MemoryStorageItem): boolean {
    if (!item.metadata.expiresAt) return false;
    return new Date() > item.metadata.expiresAt;
  }
  
  private cleanupExpired(): void {
    const now = new Date();
    const keysToDelete: string[] = [];
    
    for (const [key, item] of this.storage.entries()) {
      if (this.isExpired(item)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.storage.delete(key);
    }
    
    if (keysToDelete.length > 0) {
      console.log(`清理了 ${keysToDelete.length} 个过期项`);
    }
  }
  
  private startCleanupTimer(): void {
    // 每5分钟清理一次过期数据
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);
  }
  
  private generateETag(data: any): string {
    // 简单的ETag生成，基于数据的哈希
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `"${Math.abs(hash).toString(16)}"`;
  }
}
