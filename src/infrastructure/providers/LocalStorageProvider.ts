import { IStorageProvider, BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { 
  IStorageConfig, 
  ILocalStorageConfig,
  StorageConfigFactory 
} from '../types/StorageConfig';
import { 
  StorageResult, 
  StorageResultFactory,
  ObjectMetadata,
  GetOptions,
  PutOptions,
  ListOptions,
  BatchOptions,
  StreamOptions,
  MultipartUploadOptions,
  UploadPartInfo,
  MultipartUploadInfo,
  StorageStats,
  SignedUrlOptions
} from '../types/StorageResult';

/**
 * 本地存储提供者
 * 基于Chrome Storage API实现的本地存储
 */
export class LocalStorageProvider extends BaseStorageProvider {
  private storageKey: string = 'memorykeeper';
  private maxSize: number = 5 * 1024 * 1024; // 5MB默认限制
  private compression: boolean = false;
  
  constructor() {
    super('本地存储提供者', StorageType.LOCAL_STORAGE);
  }
  
  /**
   * 初始化本地存储提供者
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const localConfig = config as ILocalStorageConfig;
      
      // 验证配置
      this.validateConfig(localConfig);
      
      this.storageKey = localConfig.storageKey || 'memorykeeper';
      this.maxSize = localConfig.maxSize || 5 * 1024 * 1024;
      this.compression = localConfig.compression || false;
      
      this.config = config;
      
      // 测试Chrome Storage API可用性
      if (!chrome?.storage?.local) {
        throw new Error('Chrome Storage API不可用');
      }
      
      // 测试存储权限
      await this.testStorageAccess();
      
      this.initialized = true;
      console.log('本地存储提供者初始化成功');
    } catch (error) {
      console.error('本地存储提供者初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    this.initialized = false;
    console.log('本地存储提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.initialized) {
      return false;
    }
    
    try {
      await this.testStorageAccess();
      return true;
    } catch (error) {
      console.error('本地存储连接测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    this.ensureInitialized();
    
    try {
      const fullKey = this.getFullKey(key);
      
      const result = await this.withTimeout(
        this.withRetry(() => this.chromeStorageGet(fullKey))
      );
      
      if (result && result[fullKey]) {
        const data = this.decompressData(result[fullKey]);
        return StorageResultFactory.success(data, {
          contentType: 'application/json',
          lastModified: new Date(),
          etag: this.generateETag(data)
        });
      } else {
        return StorageResultFactory.failure(new Error('对象不存在'));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 存储对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    try {
      const fullKey = this.getFullKey(key);
      const compressedData = this.compressData(data);
      
      // 检查大小限制
      const dataSize = JSON.stringify(compressedData).length;
      if (dataSize > this.maxSize) {
        return StorageResultFactory.failure(
          new Error(`数据大小 ${dataSize} 超过限制 ${this.maxSize}`)
        );
      }
      
      await this.withTimeout(
        this.withRetry(() => this.chromeStorageSet({ [fullKey]: compressedData }))
      );
      
      return StorageResultFactory.success(undefined, {
        etag: this.generateETag(data)
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    try {
      const fullKey = this.getFullKey(key);
      
      await this.withTimeout(
        this.withRetry(() => this.chromeStorageRemove(fullKey))
      );
      
      return StorageResultFactory.success(undefined);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    this.ensureInitialized();
    
    try {
      const result = await this.withTimeout(
        this.withRetry(() => this.chromeStorageGetAll())
      );
      
      const fullPrefix = prefix ? this.getFullKey(prefix) : this.getFullKey('');
      const keys = Object.keys(result)
        .filter(key => key.startsWith(fullPrefix))
        .map(key => this.getRelativeKey(key))
        .slice(0, options?.maxKeys || 1000);
      
      return StorageResultFactory.success(keys);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];
    
    for (const key of keys) {
      try {
        const result = await this.get(key);
        if (result.success) {
          results[key] = result.data;
        } else {
          errors.push(`${key}: ${result.error?.message}`);
          if (!options?.continueOnError) {
            break;
          }
        }
      } catch (error) {
        errors.push(`${key}: ${(error as Error).message}`);
        if (!options?.continueOnError) {
          break;
        }
      }
    }
    
    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量获取失败: ${errors.join(', ')}`));
    }
    
    return StorageResultFactory.success(results);
  }
  
  /**
   * 批量存储对象
   */
  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    
    for (const [key, data] of Object.entries(items)) {
      try {
        const result = await this.put(key, data);
        if (!result.success) {
          errors.push(`${key}: ${result.error?.message}`);
          if (!options?.continueOnError) {
            break;
          }
        }
      } catch (error) {
        errors.push(`${key}: ${(error as Error).message}`);
        if (!options?.continueOnError) {
          break;
        }
      }
    }
    
    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量存储失败: ${errors.join(', ')}`));
    }
    
    return StorageResultFactory.success(undefined);
  }
  
  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    
    for (const key of keys) {
      try {
        const result = await this.delete(key);
        if (!result.success) {
          errors.push(`${key}: ${result.error?.message}`);
          if (!options?.continueOnError) {
            break;
          }
        }
      } catch (error) {
        errors.push(`${key}: ${(error as Error).message}`);
        if (!options?.continueOnError) {
          break;
        }
      }
    }
    
    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量删除失败: ${errors.join(', ')}`));
    }
    
    return StorageResultFactory.success(undefined);
  }
  
  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    this.ensureInitialized();
    
    try {
      const result = await this.get(key);
      if (!result.success) {
        return StorageResultFactory.failure(result.error!);
      }
      
      const data = result.data;
      const metadata: ObjectMetadata = {
        size: JSON.stringify(data).length,
        lastModified: new Date(),
        etag: this.generateETag(data),
        contentType: 'application/json'
      };
      
      return StorageResultFactory.success(metadata);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  // 以下方法在本地存储中不支持，返回错误
  async getStream(): Promise<ReadableStream> {
    throw new Error('本地存储不支持流操作');
  }
  
  async putStream(): Promise<StorageResult<void>> {
    throw new Error('本地存储不支持流操作');
  }
  
  async initiateMultipartUpload(): Promise<StorageResult<string>> {
    throw new Error('本地存储不支持分块上传');
  }
  
  async uploadPart(): Promise<StorageResult<UploadPartInfo>> {
    throw new Error('本地存储不支持分块上传');
  }
  
  async completeMultipartUpload(): Promise<StorageResult<void>> {
    throw new Error('本地存储不支持分块上传');
  }
  
  async abortMultipartUpload(): Promise<StorageResult<void>> {
    throw new Error('本地存储不支持分块上传');
  }
  
  async listMultipartUploads(): Promise<StorageResult<MultipartUploadInfo[]>> {
    throw new Error('本地存储不支持分块上传');
  }
  
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    // 本地存储返回一个模拟的URL
    return `chrome-extension://${chrome.runtime.id}/storage/${key}`;
  }
  
  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageResult<StorageStats>> {
    this.ensureInitialized();
    
    try {
      const result = await this.chromeStorageGetAll();
      const keys = Object.keys(result).filter(key => key.startsWith(this.getFullKey('')));
      
      let totalSize = 0;
      for (const key of keys) {
        totalSize += JSON.stringify(result[key]).length;
      }
      
      const stats: StorageStats = {
        totalObjects: keys.length,
        totalSize: totalSize,
        usedSpace: totalSize,
        availableSpace: this.maxSize - totalSize,
        lastModified: new Date()
      };
      
      return StorageResultFactory.success(stats);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }
  
  // 私有方法
  private validateConfig(config: ILocalStorageConfig): void {
    if (!config.storageKey) {
      throw new Error('storageKey是必需的');
    }
  }
  
  private async testStorageAccess(): Promise<void> {
    const testKey = `${this.storageKey}_test`;
    const testData = { test: true, timestamp: Date.now() };
    
    // 测试写入
    await this.chromeStorageSet({ [testKey]: testData });
    
    // 测试读取
    const result = await this.chromeStorageGet(testKey);
    if (!result || !result[testKey]) {
      throw new Error('存储读取测试失败');
    }
    
    // 清理测试数据
    await this.chromeStorageRemove(testKey);
  }
  
  private getFullKey(key: string): string {
    return `${this.storageKey}_${key}`;
  }
  
  private getRelativeKey(fullKey: string): string {
    const prefix = `${this.storageKey}_`;
    return fullKey.startsWith(prefix) ? fullKey.substring(prefix.length) : fullKey;
  }
  
  private compressData(data: any): any {
    if (this.compression) {
      // 简单的压缩实现，实际项目中可以使用更好的压缩算法
      return { compressed: true, data: JSON.stringify(data) };
    }
    return data;
  }
  
  private decompressData(data: any): any {
    if (data && data.compressed) {
      return JSON.parse(data.data);
    }
    return data;
  }
  
  private generateETag(data: any): string {
    // 简单的ETag生成，基于数据的哈希
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `"${Math.abs(hash).toString(16)}"`;
  }
  
  // Chrome Storage API包装方法
  private chromeStorageGet(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get([key], (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }
  
  private chromeStorageSet(items: Record<string, any>): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set(items, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }
  
  private chromeStorageRemove(key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.remove([key], () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }
  
  private chromeStorageGetAll(): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(null, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }
}
