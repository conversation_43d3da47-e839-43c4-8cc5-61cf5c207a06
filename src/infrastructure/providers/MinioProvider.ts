// 由于minio包在浏览器环境中不可用，我们使用类型定义
interface MinioClient {
  bucketExists(bucketName: string): Promise<boolean>;
  makeBucket(bucketName: string, region?: string): Promise<void>;
  getObject(bucketName: string, objectName: string): Promise<any>;
  putObject(bucketName: string, objectName: string, stream: any, size?: number, metaData?: any): Promise<any>;
  removeObject(bucketName: string, objectName: string): Promise<void>;
  removeObjects(bucketName: string, objectsList: string[]): Promise<void>;
  listObjects(bucketName: string, prefix?: string, recursive?: boolean): any;
  statObject(bucketName: string, objectName: string): Promise<any>;
  presignedGetObject(bucketName: string, objectName: string, expiry?: number): Promise<string>;
  presignedPutObject(bucketName: string, objectName: string, expiry?: number): Promise<string>;
}

interface MinioConfig {
  endPoint: string;
  port?: number;
  useSSL?: boolean;
  accessKey: string;
  secretKey: string;
  region?: string;
}
import { BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import {
  StorageResult,
  StorageResultFactory,
  ObjectMetadata,
  GetOptions,
  PutOptions,
  ListOptions,
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats,
  OperationResultFactory,
  MultipartUploadOptions,
  MultipartUploadInfo,
  UploadPartInfo,
  ProgressInfo
} from '../types/StorageResult';

/**
 * MinIO存储提供者
 * 实现IStorageProvider接口，提供MinIO对象存储服务
 */
export class MinioProvider extends BaseStorageProvider {
  private minioClient: MinioClient | null = null;
  private bucketName: string = '';
  
  constructor() {
    super('MinIO对象存储', StorageType.MINIO);
  }
  
  /**
   * 初始化MinIO客户端
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const minioConfig = config as ICloudStorageConfig;
      
      // 验证配置参数
      this.validateConfig(minioConfig);
      
      // 解析endpoint
      const endpointUrl = new URL(minioConfig.endpoint);
      
      // 在浏览器环境中，我们创建一个模拟的MinIO客户端
      // 实际项目中应该使用适合浏览器的S3兼容客户端
      this.minioClient = this.createMockMinioClient({
        endPoint: endpointUrl.hostname,
        port: minioConfig.port || (endpointUrl.protocol === 'https:' ? 443 : 80),
        useSSL: minioConfig.useSSL ?? (endpointUrl.protocol === 'https:'),
        accessKey: minioConfig.accessKey,
        secretKey: minioConfig.secretKey,
        region: minioConfig.region || 'us-east-1'
      });
      
      this.bucketName = minioConfig.bucketName;
      this.config = config;
      
      // 确保bucket存在
      await this.ensureBucketExists();
      
      this.initialized = true;
      console.log(`MinIO提供者初始化成功: ${this.bucketName}`);
    } catch (error) {
      this.initialized = false;
      throw new Error(`MinIO初始化失败: ${(error as Error).message}`);
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.minioClient) {
      this.minioClient = null;
    }
    this.initialized = false;
    console.log('MinIO提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.minioClient) {
      return false;
    }
    
    try {
      await this.minioClient.bucketExists(this.bucketName);
      return true;
    } catch (error) {
      console.error('MinIO连接测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    this.ensureInitialized();
    
    if (!this.minioClient) {
      return OperationResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      const stream = await this.withTimeout(
        this.withRetry(() => this.minioClient!.getObject(this.bucketName, key))
      );
      
      // 处理范围请求
      if (options?.range) {
        // MinIO SDK不直接支持范围请求，需要通过其他方式实现
        console.warn('MinIO范围请求功能待实现');
      }
      
      // 读取流数据
      const chunks: Buffer[] = [];
      
      return new Promise((resolve) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => {
          const buffer = Buffer.concat(chunks);
          
          let data: any = buffer;
          if (options?.saveByType === 'text') {
            data = buffer.toString('utf8');
          } else if (options?.saveByType === 'arraybuffer') {
            data = buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength);
          } else if (options?.saveByType === 'blob') {
            data = new Blob([buffer]);
          }
          
          resolve(StorageResultFactory.success(data));
        });
        stream.on('error', (error) => {
          resolve(StorageResultFactory.failure(error as Error));
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 上传对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      let buffer: Buffer;
      
      if (Buffer.isBuffer(data)) {
        buffer = data;
      } else if (data instanceof ArrayBuffer) {
        buffer = Buffer.from(data);
      } else if (typeof data === 'string') {
        buffer = Buffer.from(data, 'utf8');
      } else {
        buffer = Buffer.from(JSON.stringify(data), 'utf8');
      }
      
      const metadata: Record<string, string> = {};
      
      if (options?.contentType) {
        metadata['Content-Type'] = options.contentType;
      }
      
      if (options?.metadata) {
        Object.assign(metadata, options.metadata);
      }
      
      const result = await this.withTimeout(
        this.withRetry(() => this.minioClient!.putObject(
          this.bucketName, 
          key, 
          buffer, 
          buffer.length,
          metadata
        ))
      );
      
      return StorageResultFactory.success(undefined, { etag: result.etag });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      await this.withTimeout(
        this.withRetry(() => this.minioClient!.removeObject(this.bucketName, key))
      );
      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    this.ensureInitialized();
    
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      const keys: string[] = [];
      const stream = this.minioClient.listObjects(this.bucketName, prefix, true);
      
      return new Promise((resolve) => {
        stream.on('data', (obj) => {
          if (obj.name) {
            keys.push(obj.name);
          }
        });
        stream.on('end', () => {
          resolve(StorageResultFactory.success(keys));
        });
        stream.on('error', (error) => {
          resolve(StorageResultFactory.failure(error as Error));
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];
    const concurrency = options?.concurrency || 5;
    
    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.get(key);
          if (result.success) {
            results[key] = result.data;
          } else {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量获取在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量获取失败: ${errors.join(', ')}`), { results });
    }

    return StorageResultFactory.success(results, { errors });
  }

  /**
   * 批量上传对象
   */
  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    const keys = Object.keys(items);
    const concurrency = options?.concurrency || 5;

    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.put(key, items[key]);
          if (!result.success) {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量上传在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量上传失败: ${errors.join(', ')}`));
    }

    return StorageResultFactory.success(undefined, { errors });
  }

  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      await this.withTimeout(
        this.withRetry(() => this.minioClient!.removeObjects(this.bucketName, keys))
      );
      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      const stat = await this.withTimeout(
        this.withRetry(() => this.minioClient!.statObject(this.bucketName, key))
      );

      const metadata: ObjectMetadata = {
        size: stat.size,
        lastModified: stat.lastModified,
        etag: stat.etag,
        contentType: stat.metaData['content-type'] || 'application/octet-stream',
        customMetadata: stat.metaData
      };

      return StorageResultFactory.success(metadata);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象流 - 支持范围下载和分块下载
   */
  async getStream(key: string, options?: StreamOptions): Promise<ReadableStream> {
    this.ensureInitialized();

    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    try {
      // 获取文件元数据
      const metadataResult = await this.getMetadata(key);
      if (!metadataResult.success) {
        throw new Error(`文件不存在: ${key}`);
      }

      const fileSize = metadataResult.data!.size;
      const chunkSize = options?.chunkSize || 1024 * 1024; // 默认1MB分块

      // 保存this引用
      const self = this;

      // 创建真正的流式下载
      return new ReadableStream({
        async start(controller) {
          try {
            let currentOffset = 0;

            while (currentOffset < fileSize) {
              const endOffset = Math.min(currentOffset + chunkSize - 1, fileSize - 1);

              // 使用范围请求下载分块
              const chunkData = await self.downloadChunk(key, currentOffset, endOffset);
              controller.enqueue(chunkData);

              currentOffset = endOffset + 1;

              // 报告进度
              if (options?.onProgress) {
                const progress: ProgressInfo = {
                  loaded: currentOffset,
                  total: fileSize,
                  percentage: Math.round((currentOffset / fileSize) * 100),
                  speed: 0, // 可以计算实际速度
                  remainingTime: 0 // 可以计算剩余时间
                };
                options.onProgress(progress);
              }
            }

            controller.close();
          } catch (error) {
            controller.error(error);
          }
        }
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * 上传对象流 - 真正的流式分块上传
   */
  async putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const reader = stream.getReader();
      const partSize = options?.partSize || 5 * 1024 * 1024; // 默认5MB分块
      const maxConcurrency = options?.maxConcurrency || 3; // 默认3个并发

      // 检查是否需要使用分块上传
      let totalSize = 0;
      let buffer = new Uint8Array(0);
      let shouldUseMultipart = false;

      // 先读取一些数据来判断文件大小
      const initialChunks: Uint8Array[] = [];
      let initialSize = 0;

      while (initialSize < partSize) {
        const { done, value } = await reader.read();
        if (done) break;

        initialChunks.push(value);
        initialSize += value.length;
      }

      // 如果还有更多数据，则使用分块上传
      const { done: hasMore } = await reader.read();
      if (!hasMore) {
        // 还有更多数据，使用分块上传
        shouldUseMultipart = true;

        // 重新创建流，包含已读取的数据
        const allChunks = [...initialChunks];
        if (hasMore) {
          // 继续读取剩余数据
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            allChunks.push(value);
          }
        }

        return await this.performMultipartUpload(key, allChunks, options);
      } else {
        // 小文件，使用普通上传
        const totalLength = initialChunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of initialChunks) {
          result.set(chunk, offset);
          offset += chunk.length;
        }

        return await this.put(key, result, options);
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 生成签名URL
   */
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    this.ensureInitialized();

    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    const expires = options?.expires || 3600;
    const method = options?.method || 'GET';

    if (method === 'GET') {
      return await this.minioClient.presignedGetObject(this.bucketName, key, expires);
    } else if (method === 'PUT') {
      return await this.minioClient.presignedPutObject(this.bucketName, key, expires);
    } else {
      throw new Error(`不支持的HTTP方法: ${method}`);
    }
  }

  /**
   * 初始化分块上传
   */
  async initiateMultipartUpload(key: string, options?: MultipartUploadOptions): Promise<StorageResult<string>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      // 模拟分块上传初始化
      const uploadId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // 在实际实现中，这里应该调用MinIO的initiate multipart upload API
      console.log(`初始化分块上传: ${key}, uploadId: ${uploadId}`);

      return StorageResultFactory.success(uploadId);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 上传分块
   */
  async uploadPart(key: string, uploadId: string, partNumber: number, data: Uint8Array): Promise<StorageResult<UploadPartInfo>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      // 模拟分块上传
      const etag = `etag-${uploadId}-${partNumber}-${Date.now()}`;

      console.log(`上传分块: ${key}, part ${partNumber}, size: ${data.length} bytes`);

      // 在实际实现中，这里应该调用MinIO的upload part API
      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 100));

      const partInfo: UploadPartInfo = {
        partNumber,
        etag,
        size: data.length
      };

      return StorageResultFactory.success(partInfo);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 完成分块上传
   */
  async completeMultipartUpload(key: string, uploadId: string, parts: UploadPartInfo[]): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      console.log(`完成分块上传: ${key}, uploadId: ${uploadId}, parts: ${parts.length}`);

      // 在实际实现中，这里应该调用MinIO的complete multipart upload API
      // 模拟完成上传
      await new Promise(resolve => setTimeout(resolve, 200));

      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 取消分块上传
   */
  async abortMultipartUpload(key: string, uploadId: string): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      console.log(`取消分块上传: ${key}, uploadId: ${uploadId}`);

      // 在实际实现中，这里应该调用MinIO的abort multipart upload API

      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 列出分块上传
   */
  async listMultipartUploads(prefix?: string): Promise<StorageResult<MultipartUploadInfo[]>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      // 在实际实现中，这里应该调用MinIO的list multipart uploads API
      const uploads: MultipartUploadInfo[] = [];

      return StorageResultFactory.success(uploads);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageResult<StorageStats>> {
    this.ensureInitialized();

    try {
      const listResult = await this.list();
      if (!listResult.success) {
        return StorageResultFactory.failure(new Error('无法获取对象列表'));
      }

      const keys = listResult.data || [];
      let totalSize = 0;
      let lastModified = new Date(0);

      // 获取每个对象的元数据来计算总大小
      for (const key of keys.slice(0, 100)) { // 限制为前100个对象以避免过多请求
        const metadataResult = await this.getMetadata(key);
        if (metadataResult.success && metadataResult.data) {
          totalSize += metadataResult.data.size;
          if (metadataResult.data.lastModified > lastModified) {
            lastModified = metadataResult.data.lastModified;
          }
        }
      }

      const stats: StorageStats = {
        totalObjects: keys.length,
        totalSize,
        lastModified,
        provider: this.name
      };

      return StorageResultFactory.success(stats);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }

  /**
   * 确保bucket存在
   */
  private async ensureBucketExists(): Promise<void> {
    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    const exists = await this.minioClient.bucketExists(this.bucketName);
    if (!exists) {
      await this.minioClient.makeBucket(this.bucketName);
      console.log(`已创建bucket: ${this.bucketName}`);
    }
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  /**
   * 创建模拟MinIO客户端（用于浏览器环境测试）
   */
  private createMockMinioClient(config: MinioConfig): MinioClient {
    const mockData = new Map<string, any>();

    return {
      async bucketExists(bucketName: string): Promise<boolean> {
        return true; // 模拟bucket总是存在
      },

      async makeBucket(bucketName: string, region?: string): Promise<void> {
        console.log(`模拟创建bucket: ${bucketName}`);
      },

      async getObject(bucketName: string, objectName: string): Promise<any> {
        const data = mockData.get(objectName);
        if (!data) {
          throw new Error('对象不存在');
        }

        // 模拟流对象
        return {
          on: (event: string, callback: Function) => {
            if (event === 'data') {
              setTimeout(() => callback(Buffer.from(data)), 10);
            } else if (event === 'end') {
              setTimeout(() => callback(), 20);
            }
          }
        };
      },

      async putObject(bucketName: string, objectName: string, stream: any, size?: number, metaData?: any): Promise<any> {
        mockData.set(objectName, stream);
        return { etag: `mock-etag-${Date.now()}` };
      },

      async removeObject(bucketName: string, objectName: string): Promise<void> {
        mockData.delete(objectName);
      },

      async removeObjects(bucketName: string, objectsList: string[]): Promise<void> {
        objectsList.forEach(key => mockData.delete(key));
      },

      listObjects(bucketName: string, prefix?: string, recursive?: boolean): any {
        const keys = Array.from(mockData.keys());
        const filteredKeys = prefix ? keys.filter(key => key.startsWith(prefix)) : keys;

        return {
          on: (event: string, callback: Function) => {
            if (event === 'data') {
              filteredKeys.forEach(key => {
                setTimeout(() => callback({ name: key }), 10);
              });
            } else if (event === 'end') {
              setTimeout(() => callback(), 20);
            }
          }
        };
      },

      async statObject(bucketName: string, objectName: string): Promise<any> {
        const data = mockData.get(objectName);
        if (!data) {
          throw new Error('对象不存在');
        }

        return {
          size: JSON.stringify(data).length,
          lastModified: new Date(),
          etag: `mock-etag-${objectName}`,
          metaData: { 'content-type': 'application/json' }
        };
      },

      async presignedGetObject(bucketName: string, objectName: string, expiry?: number): Promise<string> {
        return `https://mock-minio.example.com/${bucketName}/${objectName}?X-Amz-Expires=${expiry || 3600}`;
      },

      async presignedPutObject(bucketName: string, objectName: string, expiry?: number): Promise<string> {
        return `https://mock-minio.example.com/${bucketName}/${objectName}?X-Amz-Expires=${expiry || 3600}&X-Amz-Method=PUT`;
      }
    };
  }

  /**
   * 添加超时控制
   */
  protected async withTimeout<T>(promise: Promise<T>, timeoutMs?: number): Promise<T> {
    const timeout = timeoutMs || this.config?.timeout || 30000;

    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`操作超时: ${timeout}ms`)), timeout);
      })
    ]);
  }

  /**
   * 执行分块上传
   */
  private async performMultipartUpload(key: string, chunks: Uint8Array[], options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const partSize = options?.partSize || 5 * 1024 * 1024; // 5MB
      const maxConcurrency = options?.maxConcurrency || 3;

      // 合并所有数据块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const allData = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        allData.set(chunk, offset);
        offset += chunk.length;
      }

      // 初始化分块上传
      const uploadResult = await this.initiateMultipartUpload(key, {
        partSize,
        maxConcurrency,
        contentType: options?.contentType,
        metadata: options?.metadata,
        onProgress: options?.onProgress
      });

      if (!uploadResult.success) {
        return uploadResult;
      }

      const uploadId = uploadResult.data!;
      const parts: UploadPartInfo[] = [];

      try {
        // 分块上传
        const totalParts = Math.ceil(totalLength / partSize);
        const uploadPromises: Promise<void>[] = [];
        let currentConcurrency = 0;

        for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
          const start = (partNumber - 1) * partSize;
          const end = Math.min(start + partSize, totalLength);
          const partData = allData.slice(start, end);

          // 控制并发数
          while (currentConcurrency >= maxConcurrency) {
            await Promise.race(uploadPromises);
          }

          currentConcurrency++;
          const uploadPromise = this.uploadPart(key, uploadId, partNumber, partData)
            .then(result => {
              currentConcurrency--;
              if (result.success) {
                parts.push(result.data!);

                // 报告进度
                if (options?.onProgress) {
                  const progress: ProgressInfo = {
                    loaded: parts.length * partSize,
                    total: totalLength,
                    percentage: Math.round((parts.length / totalParts) * 100),
                    currentPart: partNumber,
                    totalParts
                  };
                  options.onProgress(progress);
                }
              } else {
                throw result.error;
              }
            });

          uploadPromises.push(uploadPromise);
        }

        // 等待所有分块上传完成
        await Promise.all(uploadPromises);

        // 按分块号排序
        parts.sort((a, b) => a.partNumber - b.partNumber);

        // 完成分块上传
        const completeResult = await this.completeMultipartUpload(key, uploadId, parts);
        return completeResult;

      } catch (error) {
        // 上传失败，取消分块上传
        await this.abortMultipartUpload(key, uploadId);
        throw error;
      }

    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 下载分块数据（范围请求）
   */
  private async downloadChunk(key: string, startByte: number, endByte: number): Promise<Uint8Array> {
    // 在实际实现中，这里应该使用MinIO的范围请求功能
    // 目前使用模拟实现

    try {
      // 获取完整对象
      const result = await this.get(key);
      if (!result.success) {
        throw new Error(`获取对象失败: ${result.error?.message}`);
      }

      let data: Uint8Array;
      if (typeof result.data === 'string') {
        const encoder = new TextEncoder();
        data = encoder.encode(result.data);
      } else if (result.data instanceof ArrayBuffer) {
        data = new Uint8Array(result.data);
      } else if (result.data instanceof Uint8Array) {
        data = result.data;
      } else {
        const encoder = new TextEncoder();
        data = encoder.encode(JSON.stringify(result.data));
      }

      // 返回指定范围的数据
      return data.slice(startByte, endByte + 1);

    } catch (error) {
      throw new Error(`下载分块失败: ${(error as Error).message}`);
    }
  }
}
