<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华为云OBS真实测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-group {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button-group {
            margin: 15px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .result.info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .config-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-item {
            margin: 10px 0;
        }
        .config-item label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .config-item input {
            width: 300px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>华为云OBS真实环境测试</h1>
        
        <div class="config-section">
            <h3>华为云OBS配置</h3>
            <div class="config-item">
                <label>Access Key:</label>
                <input type="text" id="accessKey" value="HPUA891W6VTX56BDTC07">
            </div>
            <div class="config-item">
                <label>Secret Key:</label>
                <input type="password" id="secretKey" value="zIRyim2kuyJ34hQ7Ci5BhHNV9eQbWzrQkXA7o0dI">
            </div>
            <div class="config-item">
                <label>Bucket:</label>
                <input type="text" id="bucketName" value="gwbucket">
            </div>
            <div class="config-item">
                <label>Endpoint:</label>
                <input type="text" id="endpoint" value="https://obs.cn-north-4.myhuaweicloud.com">
            </div>
            <div class="config-item">
                <label>Region:</label>
                <input type="text" id="region" value="cn-north-4">
            </div>
            <button class="btn-primary" onclick="initializeOBS()">初始化OBS连接</button>
        </div>

        <div class="test-group">
            <h3>基础功能测试</h3>
            <div class="button-group">
                <button class="btn-primary" onclick="testConnection()">测试连接</button>
                <button class="btn-success" onclick="testBasicUpload()">基础上传</button>
                <button class="btn-info" onclick="testBasicDownload()">基础下载</button>
                <button class="btn-warning" onclick="testListObjects()">列出对象</button>
                <button class="btn-danger" onclick="clearResults()">清除结果</button>
            </div>
            <div id="basic-results" class="result info" style="display: none;"></div>
        </div>

        <div class="test-group">
            <h3>分块上传测试</h3>
            <div class="button-group">
                <button class="btn-primary" onclick="testMultipartUpload()">分块上传API</button>
                <button class="btn-success" onclick="testStreamUpload()">流式上传</button>
                <button class="btn-warning" onclick="testLargeFileUpload()">大文件上传</button>
                <button class="btn-danger" onclick="clearMultipartResults()">清除结果</button>
            </div>
            <div id="multipart-results" class="result info" style="display: none;"></div>
        </div>

        <div class="test-group">
            <h3>分块下载测试</h3>
            <div class="button-group">
                <button class="btn-primary" onclick="testStreamDownload()">流式下载</button>
                <button class="btn-success" onclick="testRangeDownload()">范围下载</button>
                <button class="btn-info" onclick="testProgressCallback()">进度回调</button>
                <button class="btn-danger" onclick="clearDownloadResults()">清除结果</button>
            </div>
            <div id="download-results" class="result info" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入华为云OBS SDK -->
    <script src="https://obs-community.obs.cn-north-1.myhuaweicloud.com/obsutil/current/jsSDK/obs-js-sdk-3.21.12.min.js"></script>
    
    <script>
        let obsProvider = null;
        let testResults = [];

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 记录测试结果
        function recordTest(passed, testName = '') {
            testResults.push({ name: testName, passed, timestamp: new Date() });
        }

        // 初始化华为云OBS连接
        window.initializeOBS = async function() {
            try {
                showResult('basic-results', '正在初始化华为云OBS连接...', 'info');

                const config = {
                    accessKey: document.getElementById('accessKey').value,
                    secretKey: document.getElementById('secretKey').value,
                    bucketName: document.getElementById('bucketName').value,
                    endpoint: document.getElementById('endpoint').value,
                    region: document.getElementById('region').value
                };

                // 创建华为云OBS提供者实例
                obsProvider = {
                    config: config,
                    obsClient: null,
                    isInitialized: false,

                    async initialize() {
                        try {
                            // 创建OBS客户端
                            this.obsClient = new ObsClient({
                                access_key_id: this.config.accessKey,
                                secret_access_key: this.config.secretKey,
                                server: this.config.endpoint,
                                timeout: 30000,
                                max_retry_count: 3
                            });

                            this.isInitialized = true;
                            console.log('华为云OBS客户端初始化成功');
                            return true;
                        } catch (error) {
                            console.error('华为云OBS初始化失败:', error);
                            throw error;
                        }
                    },

                    async testConnection() {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            this.obsClient.headBucket({
                                Bucket: this.config.bucketName
                            }, (err, result) => {
                                if (err) {
                                    console.error('连接测试失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log('连接测试成功');
                                    resolve(true);
                                } else {
                                    reject(new Error(`连接测试失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    async putObject(key, data, options = {}) {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            const params = {
                                Bucket: this.config.bucketName,
                                Key: key,
                                Body: data
                            };

                            if (options.contentType) {
                                params.ContentType = options.contentType;
                            }

                            this.obsClient.putObject(params, (err, result) => {
                                if (err) {
                                    console.error('上传失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log('上传成功:', result);
                                    resolve(result);
                                } else {
                                    reject(new Error(`上传失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    async getObject(key) {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            this.obsClient.getObject({
                                Bucket: this.config.bucketName,
                                Key: key
                            }, (err, result) => {
                                if (err) {
                                    console.error('下载失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log('下载成功');
                                    resolve(result);
                                } else {
                                    reject(new Error(`下载失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    async listObjects(prefix = '') {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            const params = {
                                Bucket: this.config.bucketName,
                                MaxKeys: 100
                            };

                            if (prefix) {
                                params.Prefix = prefix;
                            }

                            this.obsClient.listObjects(params, (err, result) => {
                                if (err) {
                                    console.error('列出对象失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log('列出对象成功');
                                    resolve(result);
                                } else {
                                    reject(new Error(`列出对象失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    async deleteObject(key) {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            this.obsClient.deleteObject({
                                Bucket: this.config.bucketName,
                                Key: key
                            }, (err, result) => {
                                if (err) {
                                    console.error('删除失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 204) {
                                    console.log('删除成功');
                                    resolve(result);
                                } else {
                                    reject(new Error(`删除失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    // 分块上传相关方法
                    async initiateMultipartUpload(key, options = {}) {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            const params = {
                                Bucket: this.config.bucketName,
                                Key: key
                            };

                            if (options.contentType) {
                                params.ContentType = options.contentType;
                            }

                            this.obsClient.initiateMultipartUpload(params, (err, result) => {
                                if (err) {
                                    console.error('初始化分块上传失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log('初始化分块上传成功:', result.InterfaceResult.UploadId);
                                    resolve(result.InterfaceResult.UploadId);
                                } else {
                                    reject(new Error(`初始化分块上传失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    async uploadPart(key, uploadId, partNumber, data) {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            this.obsClient.uploadPart({
                                Bucket: this.config.bucketName,
                                Key: key,
                                PartNumber: partNumber,
                                UploadId: uploadId,
                                Body: data
                            }, (err, result) => {
                                if (err) {
                                    console.error(`上传分块 ${partNumber} 失败:`, err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log(`上传分块 ${partNumber} 成功:`, result.InterfaceResult.ETag);
                                    resolve({
                                        partNumber: partNumber,
                                        etag: result.InterfaceResult.ETag
                                    });
                                } else {
                                    reject(new Error(`上传分块 ${partNumber} 失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    },

                    async completeMultipartUpload(key, uploadId, parts) {
                        if (!this.obsClient) {
                            throw new Error('OBS客户端未初始化');
                        }

                        return new Promise((resolve, reject) => {
                            this.obsClient.completeMultipartUpload({
                                Bucket: this.config.bucketName,
                                Key: key,
                                UploadId: uploadId,
                                MultipartUpload: {
                                    Parts: parts.map(part => ({
                                        PartNumber: part.partNumber,
                                        ETag: part.etag
                                    }))
                                }
                            }, (err, result) => {
                                if (err) {
                                    console.error('完成分块上传失败:', err);
                                    reject(err);
                                } else if (result.CommonMsg.Status === 200) {
                                    console.log('完成分块上传成功');
                                    resolve(result);
                                } else {
                                    reject(new Error(`完成分块上传失败: ${result.CommonMsg.Status}`));
                                }
                            });
                        });
                    }
                };

                // 初始化连接
                await obsProvider.initialize();

                showResult('basic-results', '✅ 华为云OBS连接初始化成功！', 'success');
                recordTest(true, 'OBS初始化');

            } catch (error) {
                const message = `❌ 华为云OBS初始化失败: ${error.message}`;
                showResult('basic-results', message, 'error');
                recordTest(false, 'OBS初始化');
            }
        };

        // 测试连接
        window.testConnection = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('basic-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('basic-results', '正在测试连接...', 'info');
                
                await obsProvider.testConnection();
                
                showResult('basic-results', '✅ 连接测试成功！', 'success');
                recordTest(true, '连接测试');
            } catch (error) {
                const message = `❌ 连接测试失败: ${error.message}`;
                showResult('basic-results', message, 'error');
                recordTest(false, '连接测试');
            }
        };

        // 测试基础上传
        window.testBasicUpload = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('basic-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('basic-results', '正在测试基础上传...', 'info');
                
                const testKey = 'test/basic-upload-test.txt';
                const testData = `华为云OBS基础上传测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;
                
                const result = await obsProvider.putObject(testKey, testData, {
                    contentType: 'text/plain'
                });
                
                showResult('basic-results', `✅ 基础上传测试成功！\n文件: ${testKey}\nETag: ${result.InterfaceResult.ETag}`, 'success');
                recordTest(true, '基础上传');
            } catch (error) {
                const message = `❌ 基础上传测试失败: ${error.message}`;
                showResult('basic-results', message, 'error');
                recordTest(false, '基础上传');
            }
        };

        // 测试基础下载
        window.testBasicDownload = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('basic-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('basic-results', '正在测试基础下载...', 'info');
                
                const testKey = 'test/basic-upload-test.txt';
                
                const result = await obsProvider.getObject(testKey);
                
                showResult('basic-results', `✅ 基础下载测试成功！\n文件: ${testKey}\n内容长度: ${result.InterfaceResult.Content.length}\n内容预览: ${result.InterfaceResult.Content.substring(0, 100)}...`, 'success');
                recordTest(true, '基础下载');
            } catch (error) {
                const message = `❌ 基础下载测试失败: ${error.message}`;
                showResult('basic-results', message, 'error');
                recordTest(false, '基础下载');
            }
        };

        // 测试列出对象
        window.testListObjects = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('basic-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('basic-results', '正在测试列出对象...', 'info');
                
                const result = await obsProvider.listObjects('test/');
                
                const objects = result.InterfaceResult.Contents || [];
                const objectList = objects.map(obj => `- ${obj.Key} (${obj.Size} bytes)`).join('\n');
                
                showResult('basic-results', `✅ 列出对象测试成功！\n找到 ${objects.length} 个对象:\n${objectList}`, 'success');
                recordTest(true, '列出对象');
            } catch (error) {
                const message = `❌ 列出对象测试失败: ${error.message}`;
                showResult('basic-results', message, 'error');
                recordTest(false, '列出对象');
            }
        };

        // 清除结果
        window.clearResults = function() {
            document.getElementById('basic-results').style.display = 'none';
        };

        window.clearMultipartResults = function() {
            document.getElementById('multipart-results').style.display = 'none';
        };

        window.clearDownloadResults = function() {
            document.getElementById('download-results').style.display = 'none';
        };

        // 测试分块上传API
        window.testMultipartUpload = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('multipart-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('multipart-results', '正在测试分块上传API...', 'info');

                const testKey = 'test/multipart-upload-test.bin';
                const partSize = 1024 * 1024; // 1MB分块
                const totalParts = 3;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                console.log(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`);
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                console.log('初始化分块上传...');
                const uploadId = await obsProvider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });

                // 2. 上传各个分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    console.log(`上传分块 ${partNumber}/${totalParts}...`);
                    const partInfo = await obsProvider.uploadPart(testKey, uploadId, partNumber, partData);
                    parts.push(partInfo);
                }

                // 3. 完成分块上传
                console.log('完成分块上传...');
                await obsProvider.completeMultipartUpload(testKey, uploadId, parts);

                // 4. 验证结果
                const listResult = await obsProvider.listObjects('test/');
                const uploadedFile = listResult.InterfaceResult.Contents.find(obj => obj.Key === testKey);

                const resultText = `✅ 分块上传API测试成功！
测试文件: ${testKey}
文件大小: ${(totalSize / (1024 * 1024)).toFixed(2)}MB
分块大小: ${(partSize / (1024 * 1024)).toFixed(2)}MB
分块数量: ${totalParts}
上传ID: ${uploadId}
验证大小: ${uploadedFile ? uploadedFile.Size : '未找到'} 字节
时间: ${new Date().toLocaleString()}`;

                showResult('multipart-results', resultText, 'success');
                recordTest(true, '分块上传API');

            } catch (error) {
                const message = `❌ 分块上传API测试失败: ${error.message}`;
                showResult('multipart-results', message, 'error');
                recordTest(false, '分块上传API');
            }
        };

        // 测试流式上传
        window.testStreamUpload = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('multipart-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('multipart-results', '正在测试流式上传...', 'info');

                const testKey = 'test/stream-upload-test.bin';
                const fileSize = 2 * 1024 * 1024; // 2MB
                const partSize = 512 * 1024; // 512KB分块

                // 模拟流式数据生成
                let uploadedSize = 0;
                const chunks = [];

                while (uploadedSize < fileSize) {
                    const chunkSize = Math.min(partSize, fileSize - uploadedSize);
                    const chunk = new Uint8Array(chunkSize);

                    // 填充随机数据
                    for (let i = 0; i < chunkSize; i++) {
                        chunk[i] = Math.floor(Math.random() * 256);
                    }

                    chunks.push(chunk);
                    uploadedSize += chunkSize;
                }

                console.log(`生成了 ${chunks.length} 个数据块，总大小: ${uploadedSize} 字节`);

                // 如果文件较小，使用普通上传
                if (fileSize < 1024 * 1024) {
                    const allData = new Uint8Array(uploadedSize);
                    let offset = 0;
                    for (const chunk of chunks) {
                        allData.set(chunk, offset);
                        offset += chunk.length;
                    }

                    await obsProvider.putObject(testKey, allData, {
                        contentType: 'application/octet-stream'
                    });
                } else {
                    // 使用分块上传
                    const uploadId = await obsProvider.initiateMultipartUpload(testKey, {
                        contentType: 'application/octet-stream'
                    });

                    const parts = [];
                    for (let i = 0; i < chunks.length; i++) {
                        const partNumber = i + 1;
                        const partInfo = await obsProvider.uploadPart(testKey, uploadId, partNumber, chunks[i]);
                        parts.push(partInfo);

                        console.log(`流式上传进度: ${Math.round((partNumber / chunks.length) * 100)}%`);
                    }

                    await obsProvider.completeMultipartUpload(testKey, uploadId, parts);
                }

                const resultText = `✅ 流式上传测试成功！
测试文件: ${testKey}
文件大小: ${(fileSize / (1024 * 1024)).toFixed(2)}MB
数据块数量: ${chunks.length}
上传方式: ${fileSize < 1024 * 1024 ? '普通上传' : '分块上传'}
时间: ${new Date().toLocaleString()}`;

                showResult('multipart-results', resultText, 'success');
                recordTest(true, '流式上传');

            } catch (error) {
                const message = `❌ 流式上传测试失败: ${error.message}`;
                showResult('multipart-results', message, 'error');
                recordTest(false, '流式上传');
            }
        };

        // 测试大文件上传
        window.testLargeFileUpload = async function() {
            if (!obsProvider || !obsProvider.isInitialized) {
                showResult('multipart-results', '请先初始化OBS连接', 'warning');
                return;
            }

            try {
                showResult('multipart-results', '正在测试大文件上传...', 'info');

                const testKey = 'test/large-file-upload-test.bin';
                const fileSizeMB = 10; // 10MB
                const partSizeMB = 2; // 2MB分块
                const fileSize = fileSizeMB * 1024 * 1024;
                const partSize = partSizeMB * 1024 * 1024;
                const totalParts = Math.ceil(fileSize / partSize);

                console.log(`开始大文件上传测试: ${fileSizeMB}MB, 分块大小: ${partSizeMB}MB, 分块数: ${totalParts}`);

                // 初始化分块上传
                const uploadId = await obsProvider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });

                const parts = [];
                const startTime = Date.now();

                // 分块上传
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = Math.min(start + partSize, fileSize);
                    const currentPartSize = end - start;

                    // 生成当前分块数据
                    const partData = new Uint8Array(currentPartSize);
                    for (let i = 0; i < currentPartSize; i++) {
                        partData[i] = Math.floor(Math.random() * 256);
                    }

                    console.log(`上传分块 ${partNumber}/${totalParts} (${(currentPartSize / (1024 * 1024)).toFixed(2)}MB)...`);

                    const partInfo = await obsProvider.uploadPart(testKey, uploadId, partNumber, partData);
                    parts.push(partInfo);

                    // 显示进度
                    const progress = Math.round((partNumber / totalParts) * 100);
                    showResult('multipart-results', `正在上传大文件... ${progress}% (${partNumber}/${totalParts})`, 'info');
                }

                // 完成分块上传
                await obsProvider.completeMultipartUpload(testKey, uploadId, parts);

                const endTime = Date.now();
                const duration = endTime - startTime;
                const speed = (fileSize / duration) * 1000 / (1024 * 1024); // MB/s

                const resultText = `✅ 大文件上传测试成功！
测试文件: ${testKey}
文件大小: ${fileSizeMB}MB
分块大小: ${partSizeMB}MB
分块数量: ${totalParts}
上传耗时: ${duration}ms
上传速度: ${speed.toFixed(2)}MB/s
时间: ${new Date().toLocaleString()}`;

                showResult('multipart-results', resultText, 'success');
                recordTest(true, '大文件上传');

            } catch (error) {
                const message = `❌ 大文件上传测试失败: ${error.message}`;
                showResult('multipart-results', message, 'error');
                recordTest(false, '大文件上传');
            }
        };

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            console.log('页面加载完成，准备测试华为云OBS');
        });
    </script>
</body>
</html>
