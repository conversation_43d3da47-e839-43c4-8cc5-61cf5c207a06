/**
 * 分块上传下载功能测试脚本
 * 用于验证新实现的分块上传下载功能
 */

// 导入必要的模块
import { MinioProvider } from '../src/infrastructure/providers/MinioProvider.js';
import { HuaweiObsProvider } from '../src/infrastructure/providers/HuaweiObsProvider.js';

// 测试配置
const testConfig = {
  minio: {
    endpoint: 'http://localhost:9000',
    accessKey: 'minioadmin',
    secretKey: 'minioadmin',
    bucketName: 'test-bucket',
    useSSL: false
  },
  huaweiObs: {
    endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
    accessKey: 'your-access-key',
    secretKey: 'your-secret-key',
    bucketName: 'test-bucket'
  }
};

/**
 * 测试分块上传功能
 */
async function testChunkedUpload(provider, testName) {
  console.log(`\n=== 测试 ${testName} 分块上传 ===`);
  
  try {
    const testKey = 'test/chunked-upload-test.bin';
    const fileSize = 10 * 1024 * 1024; // 10MB
    const partSize = 2 * 1024 * 1024; // 2MB分块
    
    // 生成测试数据
    console.log(`生成 ${fileSize / (1024 * 1024)}MB 测试数据...`);
    const testData = new Uint8Array(fileSize);
    for (let i = 0; i < fileSize; i++) {
      testData[i] = Math.floor(Math.random() * 256);
    }
    
    // 创建数据流
    const stream = new ReadableStream({
      start(controller) {
        let offset = 0;
        const chunkSize = 1024 * 1024; // 1MB块推送
        
        const pushChunk = () => {
          if (offset >= fileSize) {
            controller.close();
            return;
          }
          
          const end = Math.min(offset + chunkSize, fileSize);
          const chunk = testData.slice(offset, end);
          controller.enqueue(chunk);
          offset = end;
          
          setTimeout(pushChunk, 10);
        };
        
        pushChunk();
      }
    });
    
    // 进度回调
    let progressCount = 0;
    const onProgress = (progress) => {
      progressCount++;
      if (progress.percentage % 20 === 0) {
        console.log(`上传进度: ${progress.percentage}% (${progress.currentPart}/${progress.totalParts})`);
      }
    };
    
    // 执行分块上传
    console.log('开始分块上传...');
    const startTime = Date.now();
    
    const result = await provider.putStream(testKey, stream, {
      partSize: partSize,
      maxConcurrency: 3,
      enableProgress: true,
      onProgress: onProgress
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (result.success) {
      console.log(`✅ 分块上传成功！`);
      console.log(`   耗时: ${duration}ms`);
      console.log(`   进度更新次数: ${progressCount}`);
      
      // 验证文件
      const metadata = await provider.getMetadata(testKey);
      if (metadata.success) {
        console.log(`   文件大小验证: ${metadata.data.size === fileSize ? '通过' : '失败'}`);
      }
      
      // 清理
      await provider.delete(testKey);
      console.log(`   测试文件已清理`);
      
      return true;
    } else {
      console.log(`❌ 分块上传失败: ${result.error?.message}`);
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 分块上传测试异常: ${error.message}`);
    return false;
  }
}

/**
 * 测试分块下载功能
 */
async function testChunkedDownload(provider, testName) {
  console.log(`\n=== 测试 ${testName} 分块下载 ===`);
  
  try {
    const testKey = 'test/chunked-download-test.bin';
    const fileSize = 5 * 1024 * 1024; // 5MB
    
    // 先上传测试文件
    console.log(`上传 ${fileSize / (1024 * 1024)}MB 测试文件...`);
    const testData = new Uint8Array(fileSize);
    for (let i = 0; i < fileSize; i++) {
      testData[i] = i % 256;
    }
    
    const uploadResult = await provider.put(testKey, testData);
    if (!uploadResult.success) {
      throw new Error(`上传测试文件失败: ${uploadResult.error?.message}`);
    }
    
    // 进度回调
    let progressCount = 0;
    const onProgress = (progress) => {
      progressCount++;
      if (progress.percentage % 25 === 0) {
        console.log(`下载进度: ${progress.percentage}%`);
      }
    };
    
    // 执行分块下载
    console.log('开始分块下载...');
    const startTime = Date.now();
    
    const stream = await provider.getStream(testKey, {
      chunkSize: 1024 * 1024, // 1MB分块下载
      enableProgress: true,
      onProgress: onProgress
    });
    
    // 读取流数据
    const reader = stream.getReader();
    const chunks = [];
    let totalBytes = 0;
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      chunks.push(value);
      totalBytes += value.length;
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 验证数据
    const downloadedData = new Uint8Array(totalBytes);
    let offset = 0;
    for (const chunk of chunks) {
      downloadedData.set(chunk, offset);
      offset += chunk.length;
    }
    
    const dataMatches = downloadedData.length === testData.length &&
      downloadedData.every((byte, index) => byte === testData[index]);
    
    console.log(`✅ 分块下载完成！`);
    console.log(`   耗时: ${duration}ms`);
    console.log(`   下载大小: ${totalBytes} 字节`);
    console.log(`   分块数量: ${chunks.length}`);
    console.log(`   进度更新次数: ${progressCount}`);
    console.log(`   数据验证: ${dataMatches ? '通过' : '失败'}`);
    
    // 清理
    await provider.delete(testKey);
    console.log(`   测试文件已清理`);
    
    return dataMatches;
    
  } catch (error) {
    console.log(`❌ 分块下载测试异常: ${error.message}`);
    return false;
  }
}

/**
 * 测试分块上传API
 */
async function testMultipartAPI(provider, testName) {
  console.log(`\n=== 测试 ${testName} 分块上传API ===`);
  
  try {
    const testKey = 'test/multipart-api-test.bin';
    const partSize = 1024 * 1024; // 1MB分块
    const totalParts = 3;
    const totalSize = partSize * totalParts;
    
    // 生成测试数据
    const testData = new Uint8Array(totalSize);
    for (let i = 0; i < totalSize; i++) {
      testData[i] = Math.floor(Math.random() * 256);
    }
    
    // 1. 初始化分块上传
    console.log('初始化分块上传...');
    const initResult = await provider.initiateMultipartUpload(testKey, {
      partSize: partSize,
      contentType: 'application/octet-stream'
    });
    
    if (!initResult.success) {
      throw new Error(`初始化失败: ${initResult.error?.message}`);
    }
    
    const uploadId = initResult.data;
    console.log(`上传ID: ${uploadId}`);
    
    // 2. 上传各个分块
    const parts = [];
    for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
      const start = (partNumber - 1) * partSize;
      const end = start + partSize;
      const partData = testData.slice(start, end);
      
      console.log(`上传分块 ${partNumber}/${totalParts}...`);
      const partResult = await provider.uploadPart(testKey, uploadId, partNumber, partData);
      
      if (!partResult.success) {
        throw new Error(`分块 ${partNumber} 上传失败: ${partResult.error?.message}`);
      }
      
      parts.push(partResult.data);
    }
    
    // 3. 完成分块上传
    console.log('完成分块上传...');
    const completeResult = await provider.completeMultipartUpload(testKey, uploadId, parts);
    
    if (!completeResult.success) {
      throw new Error(`完成上传失败: ${completeResult.error?.message}`);
    }
    
    // 4. 验证结果
    const metadata = await provider.getMetadata(testKey);
    const sizeMatches = metadata.success && metadata.data.size === totalSize;
    
    console.log(`✅ 分块上传API测试完成！`);
    console.log(`   分块数量: ${parts.length}`);
    console.log(`   文件大小验证: ${sizeMatches ? '通过' : '失败'}`);
    
    // 清理
    await provider.delete(testKey);
    console.log(`   测试文件已清理`);
    
    return sizeMatches;
    
  } catch (error) {
    console.log(`❌ 分块上传API测试异常: ${error.message}`);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始分块上传下载功能测试...\n');
  
  const results = {
    minio: { upload: false, download: false, api: false },
    huaweiObs: { upload: false, download: false, api: false }
  };
  
  // 测试MinIO
  try {
    const minioProvider = new MinioProvider();
    await minioProvider.initialize(testConfig.minio);
    
    results.minio.upload = await testChunkedUpload(minioProvider, 'MinIO');
    results.minio.download = await testChunkedDownload(minioProvider, 'MinIO');
    results.minio.api = await testMultipartAPI(minioProvider, 'MinIO');
    
    await minioProvider.dispose();
  } catch (error) {
    console.log(`MinIO测试初始化失败: ${error.message}`);
  }
  
  // 测试华为云OBS（如果配置了有效的凭据）
  if (testConfig.huaweiObs.accessKey !== 'your-access-key') {
    try {
      const obsProvider = new HuaweiObsProvider();
      await obsProvider.initialize(testConfig.huaweiObs);
      
      results.huaweiObs.upload = await testChunkedUpload(obsProvider, '华为云OBS');
      results.huaweiObs.download = await testChunkedDownload(obsProvider, '华为云OBS');
      results.huaweiObs.api = await testMultipartAPI(obsProvider, '华为云OBS');
      
      await obsProvider.dispose();
    } catch (error) {
      console.log(`华为云OBS测试初始化失败: ${error.message}`);
    }
  } else {
    console.log('\n华为云OBS测试跳过（需要配置有效凭据）');
  }
  
  // 输出测试结果
  console.log('\n=== 测试结果汇总 ===');
  console.log('MinIO:');
  console.log(`  分块上传: ${results.minio.upload ? '✅' : '❌'}`);
  console.log(`  分块下载: ${results.minio.download ? '✅' : '❌'}`);
  console.log(`  分块API: ${results.minio.api ? '✅' : '❌'}`);
  
  console.log('华为云OBS:');
  console.log(`  分块上传: ${results.huaweiObs.upload ? '✅' : '❌'}`);
  console.log(`  分块下载: ${results.huaweiObs.download ? '✅' : '❌'}`);
  console.log(`  分块API: ${results.huaweiObs.api ? '✅' : '❌'}`);
  
  const allPassed = Object.values(results).every(provider => 
    Object.values(provider).every(test => test)
  );
  
  console.log(`\n总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
  
  return results;
}

// 如果直接运行此脚本
if (typeof window === 'undefined') {
  runAllTests().catch(console.error);
}

export { runAllTests, testChunkedUpload, testChunkedDownload, testMultipartAPI };
