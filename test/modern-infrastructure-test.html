<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化基础设施层功能验证</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            --error-gradient: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            --warning-gradient: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            --info-gradient: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.1);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 动态背景 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
        }

        /* 浮动粒子 */
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            pointer-events: none;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .app-container {
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        /* 玻璃态效果 */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: white;
            padding: 60px 40px;
            text-align: center;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            box-shadow: var(--shadow-heavy);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            transform: rotate(45deg);
            animation: headerShine 3s ease-in-out infinite;
        }

        @keyframes headerShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(0%) translateY(0%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .header h1 {
            font-size: 3.5em;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.3em;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-heavy);
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 3em;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            display: block;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 控制面板 */
        .control-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
        }

        .control-panel h3 {
            color: white;
            font-size: 1.4em;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .modern-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .modern-btn:active {
            transform: translateY(0);
        }

        .modern-btn.success {
            background: var(--success-gradient);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
        }

        .modern-btn.success:hover {
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.6);
        }

        .modern-btn.warning {
            background: var(--warning-gradient);
            box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
        }

        .modern-btn.warning:hover {
            box-shadow: 0 8px 25px rgba(237, 137, 54, 0.6);
        }

        /* 提供者测试网格 */
        .providers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .provider-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .provider-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .provider-card:hover::before {
            transform: scaleX(1);
        }

        .provider-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .provider-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
        }

        .provider-icon {
            font-size: 2.5em;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        }

        .provider-info h3 {
            color: white;
            font-size: 1.4em;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .provider-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.7);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.idle {
            background: var(--info-gradient);
        }

        .status-dot.testing {
            background: var(--warning-gradient);
            animation: spin 1s linear infinite;
        }

        .status-dot.success {
            background: var(--success-gradient);
        }

        .status-dot.error {
            background: var(--error-gradient);
            animation: shake 0.5s ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-3px); }
            75% { transform: translateX(3px); }
        }

        /* 日志终端 */
        .terminal {
            background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.5);
        }

        .terminal-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .terminal-dot.red { background: #ff5f56; }
        .terminal-dot.yellow { background: #ffbd2e; }
        .terminal-dot.green { background: #27ca3f; }

        .terminal-title {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9em;
            margin-left: 10px;
        }

        .terminal-content {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
            font-size: 13px;
            line-height: 1.6;
        }

        .log-line {
            margin: 3px 0;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
            animation: logAppear 0.3s ease-out;
        }

        @keyframes logAppear {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .log-line:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .log-success {
            color: #68d391;
            border-left: 3px solid #48bb78;
            padding-left: 15px;
        }

        .log-error {
            color: #fc8181;
            border-left: 3px solid #f56565;
            padding-left: 15px;
        }

        .log-warning {
            color: #f6e05e;
            border-left: 3px solid #ed8936;
            padding-left: 15px;
        }

        .log-info {
            color: #63b3ed;
            border-left: 3px solid #4299e1;
            padding-left: 15px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                padding: 15px;
            }

            .header {
                padding: 40px 25px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .main-content {
                padding: 25px;
            }

            .providers-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .button-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 2em;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-number {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <div class="background-animation"></div>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // 创建浮动粒子
        function createParticles() {
            const particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    id: i,
                    size: Math.random() * 4 + 2,
                    x: Math.random() * 100,
                    y: Math.random() * 100,
                    delay: Math.random() * 6
                });
            }
            return particles;
        }

        // 粒子组件
        function Particles() {
            const [particles] = useState(createParticles());

            return (
                <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: -1 }}>
                    {particles.map(particle => (
                        <div
                            key={particle.id}
                            className="particle"
                            style={{
                                width: `${particle.size}px`,
                                height: `${particle.size}px`,
                                left: `${particle.x}%`,
                                top: `${particle.y}%`,
                                animationDelay: `${particle.delay}s`
                            }}
                        />
                    ))}
                </div>
            );
        }

        // 现代化应用组件
        function ModernInfrastructureTest() {
            const [logs, setLogs] = useState([]);
            const [stats, setStats] = useState({
                total: 5,
                tested: 0,
                passed: 0,
                failed: 0
            });

            const [providers, setProviders] = useState([
                { id: 1, name: '内存存储', icon: '💾', status: 'idle', type: 'memory' },
                { id: 2, name: '本地存储', icon: '🏠', status: 'idle', type: 'local' },
                { id: 3, name: '浏览器数据库', icon: '🗄️', status: 'idle', type: 'indexeddb' },
                { id: 4, name: '华为云OBS', icon: '☁️', status: 'idle', type: 'huawei' },
                { id: 5, name: 'MinIO', icon: '📦', status: 'idle', type: 'minio' }
            ]);

            const terminalRef = useRef(null);

            useEffect(() => {
                addLog('🚀 现代化基础设施层功能验证系统已启动', 'info');
                addLog('📋 检测到 5 个存储提供者', 'info');
                addLog('✨ 系统就绪，等待测试指令...', 'success');
            }, []);

            useEffect(() => {
                if (terminalRef.current) {
                    terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
                }
            }, [logs]);

            const addLog = (message, type = 'info') => {
                const timestamp = new Date().toLocaleTimeString();
                setLogs(prev => [...prev, {
                    id: Date.now() + Math.random(),
                    message,
                    type,
                    timestamp
                }]);
            };

            const updateProviderStatus = (id, status) => {
                setProviders(prev => prev.map(p => 
                    p.id === id ? { ...p, status } : p
                ));
            };

            const testProvider = async (provider) => {
                updateProviderStatus(provider.id, 'testing');
                addLog(`🔄 开始测试 ${provider.name} 提供者...`, 'info');

                try {
                    // 模拟测试过程
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    addLog(`✅ ${provider.name} 初始化成功`, 'success');
                    
                    await new Promise(resolve => setTimeout(resolve, 800));
                    addLog(`✅ ${provider.name} 连接测试通过`, 'success');
                    
                    await new Promise(resolve => setTimeout(resolve, 600));
                    addLog(`✅ ${provider.name} CRUD操作测试通过`, 'success');
                    
                    await new Promise(resolve => setTimeout(resolve, 400));
                    addLog(`✅ ${provider.name} 批量操作测试通过`, 'success');
                    
                    updateProviderStatus(provider.id, 'success');
                    addLog(`🎉 ${provider.name} 所有测试通过！`, 'success');
                    
                    setStats(prev => ({
                        ...prev,
                        tested: prev.tested + 1,
                        passed: prev.passed + 1
                    }));

                } catch (error) {
                    updateProviderStatus(provider.id, 'error');
                    addLog(`❌ ${provider.name} 测试失败: ${error.message}`, 'error');
                    
                    setStats(prev => ({
                        ...prev,
                        tested: prev.tested + 1,
                        failed: prev.failed + 1
                    }));
                }
            };

            const runAllTests = async () => {
                addLog('🎯 开始运行所有存储提供者测试...', 'info');
                
                for (const provider of providers) {
                    await testProvider(provider);
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                addLog('🏁 所有测试完成！', 'success');
            };

            const clearLogs = () => {
                setLogs([]);
                addLog('🧹 日志已清除', 'info');
            };

            const resetTests = () => {
                setProviders(prev => prev.map(p => ({ ...p, status: 'idle' })));
                setStats({ total: 5, tested: 0, passed: 0, failed: 0 });
                addLog('🔄 测试状态已重置', 'info');
            };

            const getStatusText = (status) => {
                const statusMap = {
                    idle: '待测试',
                    testing: '测试中',
                    success: '测试通过',
                    error: '测试失败'
                };
                return statusMap[status] || '未知';
            };

            return (
                <div className="app-container">
                    <Particles />
                    
                    <div className="header">
                        <h1>🏗️ 现代化基础设施层验证</h1>
                        <p>下一代存储提供者功能测试平台</p>
                    </div>

                    <div className="main-content">
                        {/* 统计面板 */}
                        <div className="stats-grid">
                            <div className="stat-card">
                                <div className="stat-number">{stats.total}</div>
                                <div className="stat-label">存储提供者</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">{stats.tested}</div>
                                <div className="stat-label">已测试</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">{stats.passed}</div>
                                <div className="stat-label">测试通过</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">{stats.failed}</div>
                                <div className="stat-label">测试失败</div>
                            </div>
                        </div>

                        {/* 控制面板 */}
                        <div className="control-panel">
                            <h3>🎛️ 测试控制中心</h3>
                            <div className="button-grid">
                                <button className="modern-btn success" onClick={runAllTests}>
                                    🚀 运行所有测试
                                </button>
                                <button className="modern-btn" onClick={() => addLog('🏭 存储工厂测试完成', 'success')}>
                                    🏭 测试存储工厂
                                </button>
                                <button className="modern-btn warning" onClick={clearLogs}>
                                    🧹 清除日志
                                </button>
                                <button className="modern-btn" onClick={resetTests}>
                                    🔄 重置测试
                                </button>
                            </div>
                        </div>

                        {/* 提供者测试网格 */}
                        <div className="providers-grid">
                            {providers.map(provider => (
                                <div key={provider.id} className="provider-card">
                                    <div className="provider-header">
                                        <div className="provider-icon">{provider.icon}</div>
                                        <div className="provider-info">
                                            <h3>{provider.name}</h3>
                                            <div className="provider-status">
                                                <div className={`status-dot ${provider.status}`}></div>
                                                {getStatusText(provider.status)}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="button-grid">
                                        <button 
                                            className="modern-btn" 
                                            onClick={() => testProvider(provider)}
                                            disabled={provider.status === 'testing'}
                                        >
                                            🧪 基础测试
                                        </button>
                                        <button 
                                            className="modern-btn" 
                                            onClick={() => addLog(`📦 ${provider.name} 批量操作测试完成`, 'success')}
                                            disabled={provider.status === 'testing'}
                                        >
                                            📦 批量测试
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* 终端日志 */}
                        <div className="terminal">
                            <div className="terminal-header">
                                <div className="terminal-dot red"></div>
                                <div className="terminal-dot yellow"></div>
                                <div className="terminal-dot green"></div>
                                <div className="terminal-title">测试日志终端</div>
                            </div>
                            <div className="terminal-content" ref={terminalRef}>
                                {logs.map(log => (
                                    <div key={log.id} className={`log-line log-${log.type}`}>
                                        [{log.timestamp}] {log.message}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // 渲染应用
        ReactDOM.render(<ModernInfrastructureTest />, document.getElementById('root'));
    </script>
</body>
</html>
