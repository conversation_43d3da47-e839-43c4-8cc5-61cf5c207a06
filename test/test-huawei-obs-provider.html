<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华为云OBS Provider测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>华为云OBS Provider测试</h1>
    
    <div>
        <button class="btn-primary" onclick="testBasicOperations()">基础操作测试</button>
        <button class="btn-success" onclick="testStreamOperations()">流式操作测试</button>
        <button class="btn-warning" onclick="testMultipartAPI()">分块上传API测试</button>
        <button class="btn-danger" onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <!-- 引入华为云OBS SDK -->
    <script src="https://obs-community.obs.cn-north-1.myhuaweicloud.com/obsutil/current/jsSDK/obs-js-sdk-3.21.12.min.js"></script>
    
    <script type="module">
        // 模拟我们的HuaweiObsProvider类
        class HuaweiObsProvider {
            constructor() {
                this.obsClient = null;
                this.bucketName = '';
                this.config = null;
                this.initialized = false;
                this.name = '华为云OBS';
            }

            async initialize(config) {
                try {
                    this.obsClient = new ObsClient({
                        access_key_id: config.accessKey,
                        secret_access_key: config.secretKey,
                        server: config.endpoint,
                        timeout: config.timeout || 30000,
                        max_retry_count: config.retryCount || 3
                    });
                    
                    this.bucketName = config.bucketName;
                    this.config = config;
                    
                    // 测试连接
                    const testResult = await this.testConnection();
                    if (!testResult) {
                        throw new Error('连接测试失败');
                    }
                    
                    this.initialized = true;
                    console.log(`华为云OBS提供者初始化成功: ${this.bucketName}`);
                } catch (error) {
                    this.initialized = false;
                    throw new Error(`华为云OBS初始化失败: ${error.message}`);
                }
            }

            async testConnection() {
                if (!this.obsClient) {
                    return false;
                }
                
                try {
                    return new Promise((resolve) => {
                        this.obsClient.headBucket({
                            Bucket: this.bucketName
                        }, (err, result) => {
                            if (err) {
                                console.error('华为云OBS连接测试失败:', err);
                                resolve(false);
                            } else if (result.CommonMsg.Status === 200) {
                                console.log('华为云OBS连接测试成功');
                                resolve(true);
                            } else {
                                console.error('华为云OBS连接测试失败:', result.CommonMsg);
                                resolve(false);
                            }
                        });
                    });
                } catch (error) {
                    console.error('华为云OBS连接测试异常:', error);
                    return false;
                }
            }

            async put(key, data, options = {}) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    const params = {
                        Bucket: this.bucketName,
                        Key: key,
                        Body: data
                    };
                    
                    if (options.contentType) {
                        params.ContentType = options.contentType;
                    }
                    
                    if (options.metadata) {
                        params.Metadata = options.metadata;
                    }

                    return new Promise((resolve) => {
                        this.obsClient.putObject(params, (err, result) => {
                            if (err) {
                                console.error('上传对象失败:', err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 200) {
                                resolve({ 
                                    success: true, 
                                    data: undefined, 
                                    metadata: { etag: result.InterfaceResult.ETag }
                                });
                            } else {
                                resolve({ success: false, error: new Error(`上传对象失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }

            async get(key, options = {}) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    const params = {
                        Bucket: this.bucketName,
                        Key: key
                    };
                    
                    if (options.range) {
                        params.Range = `bytes=${options.range.start}-${options.range.end}`;
                    }

                    return new Promise((resolve) => {
                        this.obsClient.getObject(params, (err, result) => {
                            if (err) {
                                console.error('获取对象失败:', err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 200) {
                                resolve({ 
                                    success: true, 
                                    data: result.InterfaceResult.Content,
                                    metadata: {
                                        contentType: result.InterfaceResult.ContentType,
                                        lastModified: result.InterfaceResult.LastModified,
                                        etag: result.InterfaceResult.ETag
                                    }
                                });
                            } else {
                                resolve({ success: false, error: new Error(`获取对象失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }

            async delete(key) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    return new Promise((resolve) => {
                        this.obsClient.deleteObject({
                            Bucket: this.bucketName,
                            Key: key
                        }, (err, result) => {
                            if (err) {
                                console.error('删除对象失败:', err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 204) {
                                resolve({ success: true });
                            } else {
                                resolve({ success: false, error: new Error(`删除对象失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }

            async getMetadata(key) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    return new Promise((resolve) => {
                        this.obsClient.headObject({
                            Bucket: this.bucketName,
                            Key: key
                        }, (err, result) => {
                            if (err) {
                                console.error('获取元数据失败:', err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 200) {
                                const metadata = {
                                    size: parseInt(result.InterfaceResult.ContentLength),
                                    lastModified: new Date(result.InterfaceResult.LastModified),
                                    etag: result.InterfaceResult.ETag,
                                    contentType: result.InterfaceResult.ContentType,
                                    customMetadata: result.InterfaceResult.Metadata
                                };
                                resolve({ success: true, data: metadata });
                            } else {
                                resolve({ success: false, error: new Error(`获取元数据失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }

            async initiateMultipartUpload(key, options = {}) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    const params = {
                        Bucket: this.bucketName,
                        Key: key
                    };
                    
                    if (options.contentType) {
                        params.ContentType = options.contentType;
                    }
                    
                    if (options.metadata) {
                        params.Metadata = options.metadata;
                    }

                    return new Promise((resolve) => {
                        this.obsClient.initiateMultipartUpload(params, (err, result) => {
                            if (err) {
                                console.error('初始化分块上传失败:', err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 200) {
                                resolve({ success: true, data: result.InterfaceResult.UploadId });
                            } else {
                                resolve({ success: false, error: new Error(`初始化分块上传失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }

            async uploadPart(key, uploadId, partNumber, data) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    return new Promise((resolve) => {
                        this.obsClient.uploadPart({
                            Bucket: this.bucketName,
                            Key: key,
                            PartNumber: partNumber,
                            UploadId: uploadId,
                            Body: data
                        }, (err, result) => {
                            if (err) {
                                console.error(`上传分块 ${partNumber} 失败:`, err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 200) {
                                const partInfo = {
                                    partNumber: partNumber,
                                    etag: result.InterfaceResult.ETag,
                                    size: data.length
                                };
                                resolve({ success: true, data: partInfo });
                            } else {
                                resolve({ success: false, error: new Error(`上传分块失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }

            async completeMultipartUpload(key, uploadId, parts) {
                if (!this.obsClient) {
                    return { success: false, error: new Error('OBS客户端未初始化') };
                }
                
                try {
                    return new Promise((resolve) => {
                        this.obsClient.completeMultipartUpload({
                            Bucket: this.bucketName,
                            Key: key,
                            UploadId: uploadId,
                            MultipartUpload: {
                                Parts: parts.map(part => ({
                                    PartNumber: part.partNumber,
                                    ETag: part.etag
                                }))
                            }
                        }, (err, result) => {
                            if (err) {
                                console.error('完成分块上传失败:', err);
                                resolve({ success: false, error: err });
                            } else if (result.CommonMsg.Status === 200) {
                                resolve({ success: true });
                            } else {
                                resolve({ success: false, error: new Error(`完成分块上传失败: ${result.CommonMsg.Code}`) });
                            }
                        });
                    });
                } catch (error) {
                    return { success: false, error: error };
                }
            }
        }

        // 全局变量
        let obsProvider = null;

        // 华为云OBS配置
        const obsConfig = {
            accessKey: 'HPUA891W6VTX56BDTC07',
            secretKey: 'zIRyim2kuyJ34hQ7Ci5BhHNV9eQbWzrQkXA7o0dI',
            bucketName: 'gwbucket',
            endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
            region: 'cn-north-4'
        };

        // 显示结果
        function showResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 初始化Provider
        async function initProvider() {
            if (obsProvider && obsProvider.initialized) {
                return true;
            }

            try {
                obsProvider = new HuaweiObsProvider();
                await obsProvider.initialize(obsConfig);
                showResult('华为云OBS Provider初始化成功', 'success');
                return true;
            } catch (error) {
                showResult(`华为云OBS Provider初始化失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试基础操作
        window.testBasicOperations = async function() {
            showResult('开始测试基础操作...', 'info');
            
            if (!await initProvider()) {
                return;
            }

            try {
                const testKey = 'test/provider-basic-test.txt';
                const testData = `Provider基础测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await obsProvider.put(testKey, testData, {
                    contentType: 'text/plain'
                });
                
                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult(`上传成功: ${testKey}`, 'success');

                // 2. 测试获取元数据
                showResult('测试获取元数据...', 'info');
                const metadataResult = await obsProvider.getMetadata(testKey);
                
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 3. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await obsProvider.get(testKey);
                
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }
                
                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 4. 测试删除
                showResult('测试删除...', 'info');
                const deleteResult = await obsProvider.delete(testKey);
                
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('删除成功', 'success');

                showResult('✅ 基础操作测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 基础操作测试失败: ${error.message}`, 'error');
            }
        };

        // 测试分块上传API
        window.testMultipartAPI = async function() {
            showResult('开始测试分块上传API...', 'info');
            
            if (!await initProvider()) {
                return;
            }

            try {
                const testKey = 'test/provider-multipart-test.bin';
                const partSize = 1024 * 1024; // 1MB
                const totalParts = 3;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                showResult(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`, 'info');
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                showResult('初始化分块上传...', 'info');
                const initResult = await obsProvider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });
                
                if (!initResult.success) {
                    throw new Error(`初始化失败: ${initResult.error.message}`);
                }
                
                const uploadId = initResult.data;
                showResult(`初始化成功，UploadId: ${uploadId}`, 'success');

                // 2. 上传分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    showResult(`上传分块 ${partNumber}/${totalParts}...`, 'info');
                    
                    const partResult = await obsProvider.uploadPart(testKey, uploadId, partNumber, partData);
                    
                    if (!partResult.success) {
                        throw new Error(`分块 ${partNumber} 上传失败: ${partResult.error.message}`);
                    }

                    parts.push(partResult.data);
                    showResult(`分块 ${partNumber} 上传成功`, 'success');
                }

                // 3. 完成分块上传
                showResult('完成分块上传...', 'info');
                const completeResult = await obsProvider.completeMultipartUpload(testKey, uploadId, parts);
                
                if (!completeResult.success) {
                    throw new Error(`完成上传失败: ${completeResult.error.message}`);
                }
                showResult('分块上传完成', 'success');

                // 4. 验证结果
                const metadataResult = await obsProvider.getMetadata(testKey);
                const sizeMatches = metadataResult.success && metadataResult.data.size === totalSize;
                
                showResult(`文件大小验证: ${sizeMatches ? '通过' : '失败'}`, sizeMatches ? 'success' : 'error');

                // 5. 清理
                await obsProvider.delete(testKey);
                showResult('测试文件已清理', 'success');

                showResult('✅ 分块上传API测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 分块上传API测试失败: ${error.message}`, 'error');
            }
        };

        // 清除结果
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // 页面加载完成
        window.addEventListener('load', function() {
            showResult('页面加载完成，可以开始测试华为云OBS Provider', 'info');
        });
    </script>
</body>
</html>
