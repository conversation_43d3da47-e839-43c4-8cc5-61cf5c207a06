<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React基础设施层功能验证</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .test-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-card .icon {
            font-size: 1.5em;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .status-info {
            background: #17a2b8;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success {
            color: #68d391;
        }
        
        .log-error {
            color: #fc8181;
        }
        
        .log-warning {
            color: #f6e05e;
        }
        
        .log-info {
            color: #63b3ed;
        }
        
        .stats-bar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .config-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .config-panel h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .config-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .config-item label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }
        
        .config-item input,
        .config-item select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .config-item input:focus,
        .config-item select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // 模拟存储提供者类（实际项目中应该导入真实的类）
        class MockStorageProvider {
            constructor(name, type) {
                this.name = name;
                this.type = type;
                this.isInitialized = false;
                this.data = new Map();
            }

            async initialize(config) {
                await new Promise(resolve => setTimeout(resolve, 100));
                this.isInitialized = true;
                return { success: true };
            }

            async testConnection() {
                return this.isInitialized;
            }

            async get(key) {
                await new Promise(resolve => setTimeout(resolve, 50));
                const data = this.data.get(key);
                return {
                    success: data !== undefined,
                    data: data || null,
                    error: data === undefined ? new Error('对象不存在') : undefined
                };
            }

            async put(key, data) {
                await new Promise(resolve => setTimeout(resolve, 50));
                this.data.set(key, data);
                return {
                    success: true,
                    metadata: { etag: `mock-etag-${Date.now()}` }
                };
            }

            async delete(key) {
                await new Promise(resolve => setTimeout(resolve, 50));
                const existed = this.data.has(key);
                this.data.delete(key);
                return { success: true };
            }

            async list(prefix = '') {
                await new Promise(resolve => setTimeout(resolve, 50));
                const keys = Array.from(this.data.keys())
                    .filter(key => key.startsWith(prefix));
                return { success: true, data: keys };
            }

            async getStats() {
                return {
                    success: true,
                    data: {
                        totalObjects: this.data.size,
                        totalSize: Array.from(this.data.values())
                            .reduce((sum, item) => sum + JSON.stringify(item).length, 0),
                        usedSpace: this.data.size * 1024,
                        availableSpace: 1024 * 1024 * 10
                    }
                };
            }
        }

        // 模拟存储工厂
        class MockStorageFactory {
            static providers = {
                'memoryStorage': () => new MockStorageProvider('内存存储', 'memoryStorage'),
                'localStorage': () => new MockStorageProvider('本地存储', 'localStorage'),
                'huaweiObs': () => new MockStorageProvider('华为云OBS', 'huaweiObs'),
                'minio': () => new MockStorageProvider('MinIO', 'minio')
            };

            static create(type) {
                const factory = this.providers[type];
                if (!factory) {
                    throw new Error(`不支持的存储类型: ${type}`);
                }
                return factory();
            }

            static getSupportedTypes() {
                return Object.keys(this.providers);
            }
        }

        // 日志组件
        function LogContainer({ logs }) {
            const logRef = useRef(null);

            useEffect(() => {
                if (logRef.current) {
                    logRef.current.scrollTop = logRef.current.scrollHeight;
                }
            }, [logs]);

            return (
                <div className="log-container" ref={logRef}>
                    {logs.map((log, index) => (
                        <div key={index} className={`log-entry log-${log.type}`}>
                            [{log.timestamp}] {log.message}
                        </div>
                    ))}
                </div>
            );
        }

        // 存储提供者测试卡片
        function ProviderTestCard({ provider, onLog }) {
            const [status, setStatus] = useState('idle');
            const [stats, setStats] = useState(null);

            const log = (message, type = 'info') => {
                onLog({
                    message,
                    type,
                    timestamp: new Date().toLocaleTimeString(),
                    provider: provider.name
                });
            };

            const testBasicOperations = async () => {
                setStatus('testing');
                log(`开始测试 ${provider.name} 基础操作...`, 'info');

                try {
                    // 测试初始化
                    await provider.initialize({ type: provider.type, name: provider.name });
                    log('✅ 初始化成功', 'success');

                    // 测试连接
                    const connected = await provider.testConnection();
                    if (connected) {
                        log('✅ 连接测试通过', 'success');
                    } else {
                        log('❌ 连接测试失败', 'error');
                        setStatus('error');
                        return;
                    }

                    // 测试写入
                    const testData = { message: 'Hello World', timestamp: Date.now() };
                    const putResult = await provider.put('test-key', testData);
                    if (putResult.success) {
                        log('✅ 数据写入成功', 'success');
                    } else {
                        log('❌ 数据写入失败', 'error');
                        setStatus('error');
                        return;
                    }

                    // 测试读取
                    const getResult = await provider.get('test-key');
                    if (getResult.success && getResult.data) {
                        log('✅ 数据读取成功', 'success');
                    } else {
                        log('❌ 数据读取失败', 'error');
                        setStatus('error');
                        return;
                    }

                    // 测试列表
                    const listResult = await provider.list();
                    if (listResult.success) {
                        log(`✅ 列表操作成功，找到 ${listResult.data.length} 个对象`, 'success');
                    } else {
                        log('❌ 列表操作失败', 'error');
                        setStatus('error');
                        return;
                    }

                    // 测试删除
                    const deleteResult = await provider.delete('test-key');
                    if (deleteResult.success) {
                        log('✅ 数据删除成功', 'success');
                    } else {
                        log('❌ 数据删除失败', 'error');
                        setStatus('error');
                        return;
                    }

                    setStatus('success');
                    log(`🎉 ${provider.name} 所有基础操作测试通过`, 'success');

                } catch (error) {
                    log(`❌ 测试失败: ${error.message}`, 'error');
                    setStatus('error');
                }
            };

            const testBatchOperations = async () => {
                log(`开始测试 ${provider.name} 批量操作...`, 'info');

                try {
                    // 批量写入测试数据
                    const batchData = {};
                    for (let i = 0; i < 5; i++) {
                        batchData[`batch-key-${i}`] = { id: i, data: `test data ${i}` };
                    }

                    // 模拟批量操作
                    for (const [key, data] of Object.entries(batchData)) {
                        await provider.put(key, data);
                    }
                    log('✅ 批量写入操作成功', 'success');

                    // 批量读取
                    const keys = Object.keys(batchData);
                    for (const key of keys) {
                        await provider.get(key);
                    }
                    log('✅ 批量读取操作成功', 'success');

                    // 批量删除
                    for (const key of keys) {
                        await provider.delete(key);
                    }
                    log('✅ 批量删除操作成功', 'success');

                } catch (error) {
                    log(`❌ 批量操作测试失败: ${error.message}`, 'error');
                }
            };

            const getProviderStats = async () => {
                try {
                    const result = await provider.getStats();
                    if (result.success) {
                        setStats(result.data);
                        log('✅ 统计信息获取成功', 'success');
                    }
                } catch (error) {
                    log(`❌ 获取统计信息失败: ${error.message}`, 'error');
                }
            };

            const getStatusIcon = () => {
                switch (status) {
                    case 'success': return '✅';
                    case 'error': return '❌';
                    case 'testing': return '🔄';
                    default: return '⚪';
                }
            };

            const getStatusClass = () => {
                switch (status) {
                    case 'success': return 'status-success';
                    case 'error': return 'status-error';
                    case 'testing': return 'status-warning';
                    default: return 'status-info';
                }
            };

            return (
                <div className="test-card">
                    <h3>
                        <span className="icon">{getStatusIcon()}</span>
                        {provider.name}
                        <span className={`status-indicator ${getStatusClass()}`}></span>
                    </h3>
                    
                    <div className="button-group">
                        <button 
                            className="btn" 
                            onClick={testBasicOperations}
                            disabled={status === 'testing'}
                        >
                            🧪 基础操作测试
                        </button>
                        <button 
                            className="btn" 
                            onClick={testBatchOperations}
                            disabled={status === 'testing'}
                        >
                            📦 批量操作测试
                        </button>
                        <button 
                            className="btn" 
                            onClick={getProviderStats}
                            disabled={status === 'testing'}
                        >
                            📊 获取统计信息
                        </button>
                    </div>

                    {stats && (
                        <div style={{ marginTop: '15px', fontSize: '14px', color: '#6c757d' }}>
                            <strong>统计信息:</strong><br/>
                            对象数量: {stats.totalObjects} | 
                            总大小: {(stats.totalSize / 1024).toFixed(2)} KB | 
                            已用空间: {(stats.usedSpace / 1024).toFixed(2)} KB
                        </div>
                    )}
                </div>
            );
        }

        // 主应用组件
        function App() {
            const [logs, setLogs] = useState([]);
            const [providers, setProviders] = useState([]);
            const [stats, setStats] = useState({
                total: 0,
                tested: 0,
                passed: 0,
                failed: 0
            });

            useEffect(() => {
                // 初始化存储提供者
                const supportedTypes = MockStorageFactory.getSupportedTypes();
                const providerInstances = supportedTypes.map(type => 
                    MockStorageFactory.create(type)
                );
                setProviders(providerInstances);
                setStats(prev => ({ ...prev, total: providerInstances.length }));

                // 添加欢迎日志
                addLog('🚀 React基础设施层功能验证页面已加载', 'info');
                addLog(`📋 发现 ${providerInstances.length} 个存储提供者`, 'info');
            }, []);

            const addLog = (message, type = 'info', provider = 'System') => {
                const newLog = {
                    message: `[${provider}] ${message}`,
                    type,
                    timestamp: new Date().toLocaleTimeString()
                };
                setLogs(prev => [...prev, newLog]);
            };

            const runAllTests = async () => {
                addLog('🎯 开始运行所有存储提供者测试...', 'info');
                
                for (const provider of providers) {
                    addLog(`开始测试 ${provider.name}...`, 'info');
                    // 这里可以添加自动化测试逻辑
                }
            };

            const clearLogs = () => {
                setLogs([]);
                addLog('🧹 日志已清除', 'info');
            };

            const testStorageFactory = () => {
                addLog('🏭 测试存储工厂功能...', 'info');
                
                try {
                    const supportedTypes = MockStorageFactory.getSupportedTypes();
                    addLog(`✅ 支持的存储类型: ${supportedTypes.join(', ')}`, 'success');
                    
                    // 测试创建每种类型的提供者
                    supportedTypes.forEach(type => {
                        try {
                            const provider = MockStorageFactory.create(type);
                            addLog(`✅ 成功创建 ${type} 提供者: ${provider.name}`, 'success');
                        } catch (error) {
                            addLog(`❌ 创建 ${type} 提供者失败: ${error.message}`, 'error');
                        }
                    });
                    
                    addLog('🎉 存储工厂测试完成', 'success');
                } catch (error) {
                    addLog(`❌ 存储工厂测试失败: ${error.message}`, 'error');
                }
            };

            return (
                <div className="container">
                    <div className="header">
                        <h1>🏗️ React基础设施层功能验证</h1>
                        <p>验证阶段1开发的所有存储提供者功能和接口</p>
                    </div>

                    <div className="main-content">
                        <div className="stats-bar">
                            <div className="stat-item">
                                <div className="stat-number">{stats.total}</div>
                                <div className="stat-label">存储提供者</div>
                            </div>
                            <div className="stat-item">
                                <div className="stat-number">{stats.tested}</div>
                                <div className="stat-label">已测试</div>
                            </div>
                            <div className="stat-item">
                                <div className="stat-number">{stats.passed}</div>
                                <div className="stat-label">测试通过</div>
                            </div>
                            <div className="stat-item">
                                <div className="stat-number">{stats.failed}</div>
                                <div className="stat-label">测试失败</div>
                            </div>
                        </div>

                        <div className="config-panel">
                            <h4>🎛️ 测试控制面板</h4>
                            <div className="button-group">
                                <button className="btn success" onClick={runAllTests}>
                                    🚀 运行所有测试
                                </button>
                                <button className="btn" onClick={testStorageFactory}>
                                    🏭 测试存储工厂
                                </button>
                                <button className="btn warning" onClick={clearLogs}>
                                    🧹 清除日志
                                </button>
                            </div>
                        </div>

                        <div className="test-grid">
                            {providers.map((provider, index) => (
                                <ProviderTestCard 
                                    key={index} 
                                    provider={provider} 
                                    onLog={addLog}
                                />
                            ))}
                        </div>

                        <div className="test-card">
                            <h3>
                                <span className="icon">📋</span>
                                测试日志
                            </h3>
                            <LogContainer logs={logs} />
                        </div>
                    </div>
                </div>
            );
        }

        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
