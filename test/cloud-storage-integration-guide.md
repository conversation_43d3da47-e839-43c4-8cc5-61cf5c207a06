# 云存储服务集成指南

本指南说明如何在不同环境中集成真实的云存储服务SDK，以替换测试页面中的模拟实现。

## 🏗️ 华为云OBS集成

### 环境选择

华为云OBS提供了两个不同的SDK，需要根据运行环境选择：

#### 🌐 浏览器环境/Chrome扩展
```bash
npm install esdk-obs-browserjs
```

**使用示例：**
```javascript
import ObsClient from 'esdk-obs-browserjs';

// 初始化客户端
const obsClient = new ObsClient({
    access_key_id: 'your-access-key-id',
    secret_access_key: 'your-secret-access-key',
    server: 'https://obs.cn-north-4.myhuaweicloud.com'
});

// 测试连接
try {
    await obsClient.headBucket({ Bucket: 'your-bucket-name' });
    console.log('OBS连接成功');
} catch (error) {
    console.error('OBS连接失败:', error);
}
```

#### 🖥️ Node.js服务端
```bash
npm install esdk-obs-nodejs
```

**使用示例：**
```javascript
const ObsClient = require('esdk-obs-nodejs');

// 初始化客户端
const obsClient = new ObsClient({
    access_key_id: 'your-access-key-id',
    secret_access_key: 'your-secret-access-key',
    server: 'https://obs.cn-north-4.myhuaweicloud.com'
});

// 测试连接
try {
    await obsClient.headBucket({ Bucket: 'your-bucket-name' });
    console.log('OBS连接成功');
} catch (error) {
    console.error('OBS连接失败:', error);
}
```

### 基础操作示例

#### 上传对象
```javascript
// 上传文本内容
await obsClient.putObject({
    Bucket: 'your-bucket-name',
    Key: 'test-file.txt',
    Body: 'Hello, OBS!'
});

// 上传文件（Node.js环境）
const fs = require('fs');
await obsClient.putObject({
    Bucket: 'your-bucket-name',
    Key: 'upload-file.txt',
    Body: fs.createReadStream('local-file.txt')
});
```

#### 下载对象
```javascript
const result = await obsClient.getObject({
    Bucket: 'your-bucket-name',
    Key: 'test-file.txt'
});

// 获取内容
const content = result.CommonMsg.Content;
```

#### 列出对象
```javascript
const result = await obsClient.listObjects({
    Bucket: 'your-bucket-name',
    Prefix: 'folder/',
    MaxKeys: 100
});

const objects = result.CommonMsg.Contents;
```

## 📦 MinIO集成

### SDK安装
```bash
npm install minio
```

### 使用示例

#### 初始化客户端
```javascript
const Minio = require('minio');

const minioClient = new Minio.Client({
    endPoint: 'localhost',
    port: 9000,
    useSSL: false,
    accessKey: 'your-access-key',
    secretKey: 'your-secret-key'
});
```

#### 测试连接
```javascript
try {
    const exists = await minioClient.bucketExists('your-bucket-name');
    if (exists) {
        console.log('MinIO连接成功，存储桶存在');
    } else {
        console.log('MinIO连接成功，但存储桶不存在');
    }
} catch (error) {
    console.error('MinIO连接失败:', error);
}
```

#### 基础操作
```javascript
// 上传对象
await minioClient.putObject('your-bucket-name', 'test-file.txt', 'Hello, MinIO!');

// 下载对象
const stream = await minioClient.getObject('your-bucket-name', 'test-file.txt');

// 列出对象
const objectsStream = minioClient.listObjects('your-bucket-name', 'folder/', true);
objectsStream.on('data', (obj) => {
    console.log(obj.name);
});
```

## 🔧 在测试页面中集成真实SDK

### 1. 修改StorageProvider类

将测试页面中的模拟实现替换为真实的SDK调用：

```javascript
// 替换 testHuaweiObsConnection 方法
async testHuaweiObsConnection() {
    const { accessKeyId, secretAccessKey, endpoint, bucketName } = this.config;
    
    try {
        // 根据环境选择SDK
        let ObsClient;
        if (typeof window !== 'undefined') {
            // 浏览器环境
            ObsClient = (await import('esdk-obs-browserjs')).default;
        } else {
            // Node.js环境
            ObsClient = require('esdk-obs-nodejs');
        }
        
        const obsClient = new ObsClient({
            access_key_id: accessKeyId,
            secret_access_key: secretAccessKey,
            server: endpoint
        });
        
        await obsClient.headBucket({ Bucket: bucketName });
        return true;
    } catch (error) {
        throw new Error(`华为云OBS连接失败: ${error.message}`);
    }
}

// 替换 testMinioConnection 方法
async testMinioConnection() {
    const { accessKeyId, secretAccessKey, endpoint, bucketName, useSSL } = this.config;
    
    try {
        const Minio = require('minio');
        
        const endpointUrl = new URL(endpoint);
        const minioClient = new Minio.Client({
            endPoint: endpointUrl.hostname,
            port: parseInt(endpointUrl.port) || (useSSL ? 443 : 80),
            useSSL: useSSL,
            accessKey: accessKeyId,
            secretKey: secretAccessKey
        });
        
        await minioClient.bucketExists(bucketName);
        return true;
    } catch (error) {
        throw new Error(`MinIO连接失败: ${error.message}`);
    }
}
```

### 2. 实现真实的CRUD操作

```javascript
// 华为云OBS的get方法实现
async get(key, options = {}) {
    if (this.type === 'huaweiObs') {
        try {
            const result = await this.obsClient.getObject({
                Bucket: this.config.bucketName,
                Key: key
            });
            
            return {
                success: true,
                data: result.CommonMsg.Content,
                metadata: {
                    size: result.CommonMsg.ContentLength,
                    lastModified: result.CommonMsg.LastModified,
                    etag: result.CommonMsg.ETag,
                    contentType: result.CommonMsg.ContentType
                }
            };
        } catch (error) {
            return { success: false, error: error };
        }
    }
    
    // 其他类型的实现...
}

// MinIO的put方法实现
async put(key, data, options = {}) {
    if (this.type === 'minio') {
        try {
            await this.minioClient.putObject(
                this.config.bucketName,
                key,
                data,
                options.contentType
            );
            
            return { success: true };
        } catch (error) {
            return { success: false, error: error };
        }
    }
    
    // 其他类型的实现...
}
```

## 🔐 安全注意事项

### 1. 密钥管理
- ❌ **不要**在前端代码中硬编码密钥
- ✅ **使用**环境变量或安全的配置管理
- ✅ **考虑**使用临时凭证或STS令牌

### 2. 权限控制
- ✅ **最小权限原则**：只授予必要的权限
- ✅ **存储桶策略**：配置适当的访问策略
- ✅ **CORS设置**：正确配置跨域访问

### 3. 网络安全
- ✅ **使用HTTPS**：确保数据传输安全
- ✅ **VPC网络**：在私有网络中部署
- ✅ **访问日志**：启用访问日志监控

## 📚 相关文档

### 华为云OBS
- [OBS Browser SDK文档](https://support.huaweicloud.com/sdk-browserjs-devg-obs/obs_24_0001.html)
- [OBS Node.js SDK文档](https://support.huaweicloud.com/sdk-nodejs-devg-obs/obs_24_0001.html)
- [OBS API参考](https://support.huaweicloud.com/api-obs/obs_04_0001.html)

### MinIO
- [MinIO JavaScript SDK文档](https://docs.min.io/docs/javascript-client-quickstart-guide.html)
- [MinIO API参考](https://docs.min.io/docs/minio-client-complete-guide.html)

## 🚀 快速开始

1. **选择环境**：确定是浏览器环境还是Node.js环境
2. **安装SDK**：根据环境安装对应的SDK
3. **获取凭证**：从云服务提供商获取访问密钥
4. **配置连接**：在测试页面中输入配置信息
5. **测试连接**：验证配置是否正确
6. **集成代码**：将真实SDK集成到项目中

通过以上步骤，您就可以在测试页面中使用真实的云存储服务了！
