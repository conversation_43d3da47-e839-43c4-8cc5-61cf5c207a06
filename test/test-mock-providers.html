<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟Provider测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; }
    </style>
</head>
<body>
    <h1>模拟Provider测试</h1>
    <p>测试Provider的模拟SDK功能（不需要真实的SDK）</p>
    
    <div class="section">
        <h3>MinIO Provider 模拟测试</h3>
        <button class="btn-primary" onclick="testMinioMock()">测试MinIO模拟SDK</button>
        <button class="btn-warning" onclick="testMinioMultipartMock()">测试MinIO分块上传（模拟）</button>
    </div>
    
    <div class="section">
        <h3>华为云OBS Provider 模拟测试</h3>
        <button class="btn-primary" onclick="testObsMock()">测试华为云OBS模拟SDK</button>
        <button class="btn-warning" onclick="testObsMultipartMock()">测试华为云OBS分块上传（模拟）</button>
    </div>
    
    <div class="section">
        <h3>综合测试</h3>
        <button class="btn-success" onclick="testAllMock()">测试所有模拟功能</button>
        <button class="btn-danger" onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <!-- 不引入任何外部SDK，测试模拟功能 -->
    
    <script type="module">
        // 导入Provider
        import { MinioProvider } from '../src/infrastructure/providers/MinioProvider.js';
        import { HuaweiObsProvider } from '../src/infrastructure/providers/HuaweiObsProvider.js';

        // MinIO配置
        const minioConfig = {
            endpoint: 'http://127.0.0.1:9000',
            accessKey: 'FsYFOP9cOOYDyfM9odzX',
            secretKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
            bucketName: 'eversnip',
            region: 'us-east-1'
        };

        // 华为云OBS配置
        const obsConfig = {
            endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
            accessKey: 'HPUA891W6VTX56BDTC07',
            secretKey: 'zIRyim2kuyJ34hQ7Ci5BhHNV9eQbWzrQkXA7o0dI',
            bucketName: 'gwbucket',
            region: 'cn-north-4'
        };

        // 显示结果
        function showResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 测试MinIO模拟SDK
        window.testMinioMock = async function() {
            showResult('开始测试MinIO Provider（模拟SDK）...', 'info');

            try {
                showResult('创建MinioProvider（将自动使用模拟客户端）...', 'info');

                // 创建Provider
                const provider = new MinioProvider();
                await provider.initialize(minioConfig);

                showResult('MinioProvider初始化成功（使用模拟客户端）', 'success');

                // 测试基础功能
                const testKey = 'test/minio-mock-test.txt';
                const testData = `MinIO模拟测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 测试列表
                showResult('测试对象列表...', 'info');
                const listResult = await provider.list('test/');
                if (!listResult.success) {
                    throw new Error(`列表失败: ${listResult.error.message}`);
                }
                showResult(`列表成功，找到 ${listResult.data.length} 个对象`, 'success');

                // 5. 测试删除
                showResult('测试删除...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('删除成功', 'success');

                showResult('✅ MinIO Provider（模拟SDK）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ MinIO Provider（模拟SDK）测试失败: ${error.message}`, 'error');
            }
        };

        // 测试华为云OBS模拟SDK
        window.testObsMock = async function() {
            showResult('开始测试华为云OBS Provider（模拟SDK）...', 'info');

            try {
                showResult('创建HuaweiObsProvider（将自动使用模拟客户端）...', 'info');

                // 创建Provider
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                showResult('HuaweiObsProvider初始化成功（使用模拟客户端）', 'success');

                // 测试基础功能
                const testKey = 'test/obs-mock-test.txt';
                const testData = `华为云OBS模拟测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 测试列表
                showResult('测试对象列表...', 'info');
                const listResult = await provider.list('test/');
                if (!listResult.success) {
                    throw new Error(`列表失败: ${listResult.error.message}`);
                }
                showResult(`列表成功，找到 ${listResult.data.length} 个对象`, 'success');

                // 5. 测试删除
                showResult('测试删除...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('删除成功', 'success');

                showResult('✅ 华为云OBS Provider（模拟SDK）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 华为云OBS Provider（模拟SDK）测试失败: ${error.message}`, 'error');
            }
        };

        // 测试MinIO分块上传（模拟）
        window.testMinioMultipartMock = async function() {
            showResult('开始测试MinIO分块上传（模拟）...', 'info');

            try {
                const provider = new MinioProvider();
                await provider.initialize(minioConfig);

                const testKey = 'test/minio-multipart-mock.bin';
                const partSize = 1024 * 1024; // 1MB
                const totalParts = 2;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                showResult(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`, 'info');
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                showResult('初始化分块上传...', 'info');
                const initResult = await provider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });

                if (!initResult.success) {
                    throw new Error(`初始化失败: ${initResult.error.message}`);
                }

                const uploadId = initResult.data;
                showResult(`初始化成功，UploadId: ${uploadId}`, 'success');

                // 2. 上传分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    showResult(`上传分块 ${partNumber}/${totalParts}...`, 'info');

                    const partResult = await provider.uploadPart(testKey, uploadId, partNumber, partData);

                    if (!partResult.success) {
                        throw new Error(`分块 ${partNumber} 上传失败: ${partResult.error.message}`);
                    }

                    parts.push(partResult.data);
                    showResult(`分块 ${partNumber} 上传成功`, 'success');
                }

                // 3. 完成分块上传
                showResult('完成分块上传...', 'info');
                const completeResult = await provider.completeMultipartUpload(testKey, uploadId, parts);

                if (!completeResult.success) {
                    throw new Error(`完成上传失败: ${completeResult.error.message}`);
                }
                showResult('分块上传完成', 'success');

                // 4. 清理
                await provider.delete(testKey);
                showResult('测试文件已清理', 'success');

                showResult('✅ MinIO分块上传（模拟）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ MinIO分块上传（模拟）测试失败: ${error.message}`, 'error');
            }
        };

        // 测试所有模拟功能
        window.testAllMock = async function() {
            showResult('开始综合测试所有模拟功能...', 'info');

            try {
                await testMinioMock();
                showResult('---', 'info');
                await testObsMock();
                showResult('---', 'info');
                await testMinioMultipartMock();

                showResult('🎉 所有模拟功能测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 综合测试失败: ${error.message}`, 'error');
            }
        };

        // 清除结果
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // 页面加载完成
        window.addEventListener('load', function() {
            showResult('页面加载完成，开始测试模拟Provider功能', 'info');
            showResult('注意：此页面不加载任何外部SDK，完全使用模拟客户端', 'warning');
        });
    </script>
</body>
</html>
