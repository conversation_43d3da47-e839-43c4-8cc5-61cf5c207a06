<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储提供者完整方法测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --success: #48bb78;
            --error: #f56565;
            --warning: #ed8936;
            --info: #4299e1;
            --bg-primary: #f7fafc;
            --bg-secondary: #edf2f7;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --border: #e2e8f0;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary) 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1em;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 30px;
            box-shadow: var(--shadow-lg);
        }

        .provider-selector {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 12px;
            border: 1px solid var(--border);
        }

        .provider-selector h3 {
            margin-bottom: 15px;
            color: var(--text-primary);
        }

        .provider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .provider-card {
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid var(--border);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .provider-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .provider-card.active {
            border-color: var(--primary);
            background: rgba(102, 126, 234, 0.1);
        }

        .provider-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .provider-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .test-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .test-section {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid var(--border);
        }

        .test-section h4 {
            margin-bottom: 20px;
            color: var(--text-primary);
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-methods {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .method-test {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .method-test:hover {
            box-shadow: var(--shadow);
        }

        .method-info {
            flex: 1;
        }

        .method-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .method-desc {
            font-size: 0.9em;
            color: var(--text-secondary);
        }

        .method-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-idle {
            background: var(--info);
        }

        .status-testing {
            background: var(--warning);
            animation: spin 1s linear infinite;
        }

        .status-success {
            background: var(--success);
        }

        .status-error {
            background: var(--error);
            animation: shake 0.5s ease-in-out;
        }

        .test-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .test-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .control-panel {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
        }

        .control-panel h3 {
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .control-btn.success {
            background: var(--success);
        }

        .control-btn.warning {
            background: var(--warning);
        }

        .control-btn.danger {
            background: var(--error);
        }

        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid var(--border);
            box-shadow: var(--shadow);
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .log-terminal {
            background: #1a202c;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid #2d3748;
        }

        .terminal-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #2d3748;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .terminal-dot.red { background: #ff5f56; }
        .terminal-dot.yellow { background: #ffbd2e; }
        .terminal-dot.green { background: #27ca3f; }

        .terminal-title {
            color: #a0aec0;
            font-size: 0.9em;
            margin-left: 10px;
        }

        .terminal-content {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'SF Mono', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.6;
        }

        .log-entry {
            margin: 3px 0;
            padding: 5px 10px;
            border-radius: 4px;
            animation: logAppear 0.3s ease-out;
        }

        @keyframes logAppear {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .log-success { color: #68d391; border-left: 3px solid var(--success); padding-left: 15px; }
        .log-error { color: #fc8181; border-left: 3px solid var(--error); padding-left: 15px; }
        .log-warning { color: #f6e05e; border-left: 3px solid var(--warning); padding-left: 15px; }
        .log-info { color: #63b3ed; border-left: 3px solid var(--info); padding-left: 15px; }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-3px); }
            75% { transform: translateX(3px); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .header { padding: 30px 20px; }
            .header h1 { font-size: 2em; }
            .test-sections { grid-template-columns: 1fr; }
            .provider-grid { grid-template-columns: repeat(2, 1fr); }
            .stats-panel { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            .header h1 { font-size: 1.8em; }
            .provider-grid { grid-template-columns: 1fr; }
            .stats-panel { grid-template-columns: 1fr; }
            .control-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div id="root"></div>