<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新后的华为云OBS Provider测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
    </style>
</head>
<body>
    <h1>更新后的华为云OBS Provider测试</h1>
    
    <div>
        <button class="btn-primary" onclick="testWithRealSDK()">使用真实SDK测试</button>
        <button class="btn-success" onclick="testWithMockSDK()">使用模拟SDK测试</button>
        <button class="btn-warning" onclick="testStreamOperations()">测试流式操作</button>
        <button class="btn-info" onclick="testMultipartAPI()">测试分块上传API</button>
        <button class="btn-danger" onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <!-- 引入华为云OBS SDK -->
    <script src="https://obs-community.obs.cn-north-1.myhuaweicloud.com/obsutil/current/jsSDK/obs-js-sdk-3.21.12.min.js"></script>
    
    <script type="module">
        // 导入HuaweiObsProvider
        import { HuaweiObsProvider } from '../src/infrastructure/providers/HuaweiObsProvider.js';

        // 华为云OBS配置
        const obsConfig = {
            endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
            accessKey: 'HPUA891W6VTX56BDTC07',
            secretKey: 'zIRyim2kuyJ34hQ7Ci5BhHNV9eQbWzrQkXA7o0dI',
            bucketName: 'gwbucket',
            region: 'cn-north-4'
        };

        // 显示结果
        function showResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 使用真实SDK测试
        window.testWithRealSDK = async function() {
            showResult('开始使用真实华为云OBS SDK测试...', 'info');

            try {
                // 检查SDK是否加载
                if (typeof ObsClient === 'undefined') {
                    throw new Error('华为云OBS SDK未加载');
                }

                showResult('华为云OBS SDK已加载，创建Provider...', 'info');

                // 创建Provider
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                showResult('Provider初始化成功，开始测试基础功能...', 'success');

                // 测试基础功能
                const testKey = 'test/real-sdk-test.txt';
                const testData = `真实SDK测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 清理
                showResult('清理测试文件...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('清理成功', 'success');

                showResult('✅ 真实SDK测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 真实SDK测试失败: ${error.message}`, 'error');
            }
        };

        // 使用模拟SDK测试
        window.testWithMockSDK = async function() {
            showResult('开始使用模拟SDK测试...', 'info');

            try {
                // 临时隐藏真实SDK
                const realObsClient = window.ObsClient;
                delete window.ObsClient;

                showResult('已隐藏真实SDK，将使用模拟客户端...', 'warning');

                // 创建Provider
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                showResult('Provider初始化成功（使用模拟客户端），开始测试...', 'success');

                // 测试基础功能
                const testKey = 'test/mock-sdk-test.txt';
                const testData = `模拟SDK测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 测试删除
                showResult('测试删除...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('删除成功', 'success');

                // 恢复真实SDK
                window.ObsClient = realObsClient;
                showResult('已恢复真实SDK', 'info');

                showResult('✅ 模拟SDK测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 模拟SDK测试失败: ${error.message}`, 'error');
                // 确保恢复真实SDK
                if (typeof realObsClient !== 'undefined') {
                    window.ObsClient = realObsClient;
                }
            }
        };

        // 测试分块上传API
        window.testMultipartAPI = async function() {
            showResult('开始测试分块上传API...', 'info');

            try {
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                const testKey = 'test/multipart-api-test.bin';
                const partSize = 1024 * 1024; // 1MB
                const totalParts = 3;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                showResult(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`, 'info');
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                showResult('初始化分块上传...', 'info');
                const initResult = await provider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });

                if (!initResult.success) {
                    throw new Error(`初始化失败: ${initResult.error.message}`);
                }

                const uploadId = initResult.data;
                showResult(`初始化成功，UploadId: ${uploadId}`, 'success');

                // 2. 上传分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    showResult(`上传分块 ${partNumber}/${totalParts}...`, 'info');

                    const partResult = await provider.uploadPart(testKey, uploadId, partNumber, partData);

                    if (!partResult.success) {
                        throw new Error(`分块 ${partNumber} 上传失败: ${partResult.error.message}`);
                    }

                    parts.push(partResult.data);
                    showResult(`分块 ${partNumber} 上传成功`, 'success');
                }

                // 3. 完成分块上传
                showResult('完成分块上传...', 'info');
                const completeResult = await provider.completeMultipartUpload(testKey, uploadId, parts);

                if (!completeResult.success) {
                    throw new Error(`完成上传失败: ${completeResult.error.message}`);
                }
                showResult('分块上传完成', 'success');

                // 4. 验证结果
                const metadataResult = await provider.getMetadata(testKey);
                const sizeMatches = metadataResult.success && metadataResult.data.size >= totalSize;

                showResult(`文件大小验证: ${sizeMatches ? '通过' : '失败'}`, sizeMatches ? 'success' : 'warning');

                // 5. 清理
                await provider.delete(testKey);
                showResult('测试文件已清理', 'success');

                showResult('✅ 分块上传API测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 分块上传API测试失败: ${error.message}`, 'error');
            }
        };

        // 清除结果
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // 页面加载完成
        window.addEventListener('load', function() {
            showResult('页面加载完成，可以开始测试更新后的华为云OBS Provider', 'info');
            showResult('提示：真实SDK测试需要网络连接，模拟SDK测试可以离线运行', 'warning');
        });
    </script>
</body>
</html>
