<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华为云OBS简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; white-space: pre-wrap; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>华为云OBS简单测试</h1>
    
    <div>
        <button class="btn-primary" onclick="testBasicFunctions()">测试基本功能</button>
        <button class="btn-success" onclick="testMultipartUpload()">测试分块上传</button>
    </div>
    
    <div id="results"></div>

    <!-- 引入华为云OBS SDK -->
    <script src="https://obs-community.obs.cn-north-1.myhuaweicloud.com/obsutil/current/jsSDK/obs-js-sdk-3.21.12.min.js"></script>
    
    <script>
        // 华为云OBS配置
        const obsConfig = {
            accessKey: 'HPUA891W6VTX56BDTC07',
            secretKey: 'zIRyim2kuyJ34hQ7Ci5BhHNV9eQbWzrQkXA7o0dI',
            bucketName: 'gwbucket',
            endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
            region: 'cn-north-4'
        };

        let obsClient = null;

        // 显示结果
        function showResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 初始化OBS客户端
        function initOBS() {
            try {
                obsClient = new ObsClient({
                    access_key_id: obsConfig.accessKey,
                    secret_access_key: obsConfig.secretKey,
                    server: obsConfig.endpoint,
                    timeout: 30000,
                    max_retry_count: 3
                });
                showResult('OBS客户端初始化成功', 'success');
                return true;
            } catch (error) {
                showResult(`OBS客户端初始化失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试基本功能
        window.testBasicFunctions = async function() {
            showResult('开始测试基本功能...', 'info');
            
            if (!initOBS()) {
                return;
            }

            try {
                // 1. 测试连接
                showResult('测试连接...', 'info');
                await new Promise((resolve, reject) => {
                    obsClient.headBucket({
                        Bucket: obsConfig.bucketName
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 200) {
                            showResult('连接测试成功', 'success');
                            resolve();
                        } else {
                            reject(new Error(`连接失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                // 2. 测试上传
                showResult('测试上传...', 'info');
                const testKey = 'test/simple-test.txt';
                const testData = `测试数据\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;
                
                await new Promise((resolve, reject) => {
                    obsClient.putObject({
                        Bucket: obsConfig.bucketName,
                        Key: testKey,
                        Body: testData,
                        ContentType: 'text/plain'
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 200) {
                            showResult(`上传成功: ${testKey}`, 'success');
                            resolve();
                        } else {
                            reject(new Error(`上传失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                // 3. 测试下载
                showResult('测试下载...', 'info');
                await new Promise((resolve, reject) => {
                    obsClient.getObject({
                        Bucket: obsConfig.bucketName,
                        Key: testKey
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 200) {
                            showResult(`下载成功，内容长度: ${result.InterfaceResult.Content.length}`, 'success');
                            resolve();
                        } else {
                            reject(new Error(`下载失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                // 4. 测试删除
                showResult('测试删除...', 'info');
                await new Promise((resolve, reject) => {
                    obsClient.deleteObject({
                        Bucket: obsConfig.bucketName,
                        Key: testKey
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 204) {
                            showResult('删除成功', 'success');
                            resolve();
                        } else {
                            reject(new Error(`删除失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                showResult('✅ 所有基本功能测试通过！', 'success');

            } catch (error) {
                showResult(`❌ 基本功能测试失败: ${error.message}`, 'error');
            }
        };

        // 测试分块上传
        window.testMultipartUpload = async function() {
            showResult('开始测试分块上传...', 'info');
            
            if (!initOBS()) {
                return;
            }

            try {
                const testKey = 'test/multipart-test.bin';
                const partSize = 1024 * 1024; // 1MB
                const totalParts = 3;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                showResult(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`, 'info');
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                showResult('初始化分块上传...', 'info');
                const uploadId = await new Promise((resolve, reject) => {
                    obsClient.initiateMultipartUpload({
                        Bucket: obsConfig.bucketName,
                        Key: testKey,
                        ContentType: 'application/octet-stream'
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 200) {
                            showResult(`初始化成功，UploadId: ${result.InterfaceResult.UploadId}`, 'success');
                            resolve(result.InterfaceResult.UploadId);
                        } else {
                            reject(new Error(`初始化失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                // 2. 上传分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    showResult(`上传分块 ${partNumber}/${totalParts}...`, 'info');
                    
                    const etag = await new Promise((resolve, reject) => {
                        obsClient.uploadPart({
                            Bucket: obsConfig.bucketName,
                            Key: testKey,
                            PartNumber: partNumber,
                            UploadId: uploadId,
                            Body: partData
                        }, (err, result) => {
                            if (err) {
                                reject(err);
                            } else if (result.CommonMsg.Status === 200) {
                                showResult(`分块 ${partNumber} 上传成功`, 'success');
                                resolve(result.InterfaceResult.ETag);
                            } else {
                                reject(new Error(`分块 ${partNumber} 上传失败: ${result.CommonMsg.Status}`));
                            }
                        });
                    });

                    parts.push({
                        PartNumber: partNumber,
                        ETag: etag
                    });
                }

                // 3. 完成分块上传
                showResult('完成分块上传...', 'info');
                await new Promise((resolve, reject) => {
                    obsClient.completeMultipartUpload({
                        Bucket: obsConfig.bucketName,
                        Key: testKey,
                        UploadId: uploadId,
                        MultipartUpload: {
                            Parts: parts
                        }
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 200) {
                            showResult('分块上传完成', 'success');
                            resolve();
                        } else {
                            reject(new Error(`完成上传失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                // 4. 验证文件
                showResult('验证上传结果...', 'info');
                await new Promise((resolve, reject) => {
                    obsClient.headObject({
                        Bucket: obsConfig.bucketName,
                        Key: testKey
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else if (result.CommonMsg.Status === 200) {
                            const fileSize = parseInt(result.InterfaceResult.ContentLength);
                            showResult(`文件验证成功，大小: ${fileSize} 字节`, 'success');
                            resolve();
                        } else {
                            reject(new Error(`文件验证失败: ${result.CommonMsg.Status}`));
                        }
                    });
                });

                // 5. 清理测试文件
                await new Promise((resolve, reject) => {
                    obsClient.deleteObject({
                        Bucket: obsConfig.bucketName,
                        Key: testKey
                    }, (err, result) => {
                        if (err) {
                            reject(err);
                        } else {
                            showResult('测试文件已清理', 'success');
                            resolve();
                        }
                    });
                });

                showResult('✅ 分块上传测试完全成功！', 'success');

            } catch (error) {
                showResult(`❌ 分块上传测试失败: ${error.message}`, 'error');
            }
        };

        // 页面加载完成
        window.addEventListener('load', function() {
            showResult('页面加载完成，可以开始测试', 'info');
        });
    </script>
</body>
</html>
