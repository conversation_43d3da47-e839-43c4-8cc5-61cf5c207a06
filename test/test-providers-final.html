<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终Provider测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; }
    </style>
</head>
<body>
    <h1>最终Provider测试</h1>
    <p>测试更新后的MinioProvider（使用AWS S3 SDK）和HuaweiObsProvider（使用华为云OBS SDK）</p>
    
    <div class="section">
        <h3>MinIO Provider 测试（AWS S3 SDK）</h3>
        <button class="btn-primary" onclick="testMinioWithRealSDK()">使用真实AWS S3 SDK测试</button>
        <button class="btn-success" onclick="testMinioWithMockSDK()">使用模拟SDK测试</button>
        <button class="btn-warning" onclick="testMinioMultipart()">测试MinIO分块上传</button>
    </div>
    
    <div class="section">
        <h3>华为云OBS Provider 测试</h3>
        <button class="btn-primary" onclick="testObsWithRealSDK()">使用真实华为云OBS SDK测试</button>
        <button class="btn-success" onclick="testObsWithMockSDK()">使用模拟SDK测试</button>
        <button class="btn-warning" onclick="testObsMultipart()">测试华为云OBS分块上传</button>
    </div>
    
    <div class="section">
        <h3>综合测试</h3>
        <button class="btn-info" onclick="testBothProviders()">同时测试两个Provider</button>
        <button class="btn-danger" onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <!-- 引入AWS SDK -->
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1.24.min.js"></script>
    
    <!-- 引入华为云OBS SDK -->
    <script src="https://obs-community.obs.cn-north-1.myhuaweicloud.com/obsutil/current/jsSDK/obs-js-sdk-3.21.12.min.js"></script>
    
    <script type="module">
        // 导入Provider
        import { MinioProvider } from '../src/infrastructure/providers/MinioProvider.js';
        import { HuaweiObsProvider } from '../src/infrastructure/providers/HuaweiObsProvider.js';

        // MinIO配置
        const minioConfig = {
            endpoint: 'http://127.0.0.1:9000',
            accessKey: 'FsYFOP9cOOYDyfM9odzX',
            secretKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
            bucketName: 'eversnip',
            region: 'us-east-1'
        };

        // 华为云OBS配置
        const obsConfig = {
            endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
            accessKey: 'HPUA891W6VTX56BDTC07',
            secretKey: 'zIRyim2kuyJ34hQ7Ci5BhHNV9eQbWzrQkXA7o0dI',
            bucketName: 'gwbucket',
            region: 'cn-north-4'
        };

        // 显示结果
        function showResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 测试MinIO Provider（真实AWS S3 SDK）
        window.testMinioWithRealSDK = async function() {
            showResult('开始测试MinIO Provider（使用真实AWS S3 SDK）...', 'info');

            try {
                // 检查AWS SDK是否加载
                if (typeof AWS === 'undefined') {
                    throw new Error('AWS SDK未加载');
                }

                showResult('AWS SDK已加载，创建MinioProvider...', 'info');

                // 创建Provider
                const provider = new MinioProvider();
                await provider.initialize(minioConfig);

                showResult('MinioProvider初始化成功，开始测试基础功能...', 'success');

                // 测试基础功能
                const testKey = 'test/minio-real-sdk-test.txt';
                const testData = `MinIO真实SDK测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 清理
                showResult('清理测试文件...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('清理成功', 'success');

                showResult('✅ MinIO Provider（真实SDK）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ MinIO Provider（真实SDK）测试失败: ${error.message}`, 'error');
            }
        };

        // 测试MinIO Provider（模拟SDK）
        window.testMinioWithMockSDK = async function() {
            showResult('开始测试MinIO Provider（使用模拟SDK）...', 'info');

            try {
                // 临时隐藏真实SDK
                const realAWS = window.AWS;
                delete window.AWS;

                showResult('已隐藏真实SDK，将使用模拟客户端...', 'warning');

                // 创建Provider
                const provider = new MinioProvider();
                await provider.initialize(minioConfig);

                showResult('MinioProvider初始化成功（使用模拟客户端），开始测试...', 'success');

                // 测试基础功能
                const testKey = 'test/minio-mock-sdk-test.txt';
                const testData = `MinIO模拟SDK测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 测试删除
                showResult('测试删除...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('删除成功', 'success');

                // 恢复真实SDK
                window.AWS = realAWS;
                showResult('已恢复真实SDK', 'info');

                showResult('✅ MinIO Provider（模拟SDK）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ MinIO Provider（模拟SDK）测试失败: ${error.message}`, 'error');
                // 确保恢复真实SDK
                if (typeof realAWS !== 'undefined') {
                    window.AWS = realAWS;
                }
            }
        };

        // 测试华为云OBS Provider（真实SDK）
        window.testObsWithRealSDK = async function() {
            showResult('开始测试华为云OBS Provider（使用真实SDK）...', 'info');

            try {
                // 检查华为云OBS SDK是否加载
                if (typeof ObsClient === 'undefined') {
                    throw new Error('华为云OBS SDK未加载');
                }

                showResult('华为云OBS SDK已加载，创建HuaweiObsProvider...', 'info');

                // 创建Provider
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                showResult('HuaweiObsProvider初始化成功，开始测试基础功能...', 'success');

                // 测试基础功能
                const testKey = 'test/obs-real-sdk-test.txt';
                const testData = `华为云OBS真实SDK测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 清理
                showResult('清理测试文件...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('清理成功', 'success');

                showResult('✅ 华为云OBS Provider（真实SDK）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 华为云OBS Provider（真实SDK）测试失败: ${error.message}`, 'error');
            }
        };

        // 测试华为云OBS Provider（模拟SDK）
        window.testObsWithMockSDK = async function() {
            showResult('开始测试华为云OBS Provider（使用模拟SDK）...', 'info');

            try {
                // 临时隐藏真实SDK
                const realObsClient = window.ObsClient;
                delete window.ObsClient;

                showResult('已隐藏真实SDK，将使用模拟客户端...', 'warning');

                // 创建Provider
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                showResult('HuaweiObsProvider初始化成功（使用模拟客户端），开始测试...', 'success');

                // 测试基础功能
                const testKey = 'test/obs-mock-sdk-test.txt';
                const testData = `华为云OBS模拟SDK测试\n时间: ${new Date().toISOString()}\n随机数: ${Math.random()}`;

                // 1. 测试上传
                showResult('测试上传...', 'info');
                const putResult = await provider.put(testKey, testData, {
                    contentType: 'text/plain'
                });

                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error.message}`);
                }
                showResult('上传成功', 'success');

                // 2. 测试下载
                showResult('测试下载...', 'info');
                const getResult = await provider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`下载失败: ${getResult.error.message}`);
                }

                const dataMatches = getResult.data === testData;
                showResult(`下载成功，数据验证: ${dataMatches ? '通过' : '失败'}`, dataMatches ? 'success' : 'error');

                // 3. 测试元数据
                showResult('测试元数据获取...', 'info');
                const metadataResult = await provider.getMetadata(testKey);
                if (!metadataResult.success) {
                    throw new Error(`获取元数据失败: ${metadataResult.error.message}`);
                }
                showResult(`元数据获取成功，文件大小: ${metadataResult.data.size} 字节`, 'success');

                // 4. 测试删除
                showResult('测试删除...', 'info');
                const deleteResult = await provider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error.message}`);
                }
                showResult('删除成功', 'success');

                // 恢复真实SDK
                window.ObsClient = realObsClient;
                showResult('已恢复真实SDK', 'info');

                showResult('✅ 华为云OBS Provider（模拟SDK）测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 华为云OBS Provider（模拟SDK）测试失败: ${error.message}`, 'error');
                // 确保恢复真实SDK
                if (typeof realObsClient !== 'undefined') {
                    window.ObsClient = realObsClient;
                }
            }
        };

        // 测试MinIO分块上传
        window.testMinioMultipart = async function() {
            showResult('开始测试MinIO分块上传...', 'info');

            try {
                const provider = new MinioProvider();
                await provider.initialize(minioConfig);

                const testKey = 'test/minio-multipart-test.bin';
                const partSize = 1024 * 1024; // 1MB
                const totalParts = 3;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                showResult(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`, 'info');
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                showResult('初始化分块上传...', 'info');
                const initResult = await provider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });

                if (!initResult.success) {
                    throw new Error(`初始化失败: ${initResult.error.message}`);
                }

                const uploadId = initResult.data;
                showResult(`初始化成功，UploadId: ${uploadId}`, 'success');

                // 2. 上传分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    showResult(`上传分块 ${partNumber}/${totalParts}...`, 'info');

                    const partResult = await provider.uploadPart(testKey, uploadId, partNumber, partData);

                    if (!partResult.success) {
                        throw new Error(`分块 ${partNumber} 上传失败: ${partResult.error.message}`);
                    }

                    parts.push(partResult.data);
                    showResult(`分块 ${partNumber} 上传成功`, 'success');
                }

                // 3. 完成分块上传
                showResult('完成分块上传...', 'info');
                const completeResult = await provider.completeMultipartUpload(testKey, uploadId, parts);

                if (!completeResult.success) {
                    throw new Error(`完成上传失败: ${completeResult.error.message}`);
                }
                showResult('分块上传完成', 'success');

                // 4. 验证结果
                const metadataResult = await provider.getMetadata(testKey);
                const sizeMatches = metadataResult.success && metadataResult.data.size >= totalSize;

                showResult(`文件大小验证: ${sizeMatches ? '通过' : '失败'}`, sizeMatches ? 'success' : 'warning');

                // 5. 清理
                await provider.delete(testKey);
                showResult('测试文件已清理', 'success');

                showResult('✅ MinIO分块上传测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ MinIO分块上传测试失败: ${error.message}`, 'error');
            }
        };

        // 测试华为云OBS分块上传
        window.testObsMultipart = async function() {
            showResult('开始测试华为云OBS分块上传...', 'info');

            try {
                const provider = new HuaweiObsProvider();
                await provider.initialize(obsConfig);

                const testKey = 'test/obs-multipart-test.bin';
                const partSize = 1024 * 1024; // 1MB
                const totalParts = 3;
                const totalSize = partSize * totalParts;

                // 生成测试数据
                showResult(`生成 ${totalSize / (1024 * 1024)}MB 测试数据...`, 'info');
                const testData = new Uint8Array(totalSize);
                for (let i = 0; i < totalSize; i++) {
                    testData[i] = Math.floor(Math.random() * 256);
                }

                // 1. 初始化分块上传
                showResult('初始化分块上传...', 'info');
                const initResult = await provider.initiateMultipartUpload(testKey, {
                    contentType: 'application/octet-stream'
                });

                if (!initResult.success) {
                    throw new Error(`初始化失败: ${initResult.error.message}`);
                }

                const uploadId = initResult.data;
                showResult(`初始化成功，UploadId: ${uploadId}`, 'success');

                // 2. 上传分块
                const parts = [];
                for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
                    const start = (partNumber - 1) * partSize;
                    const end = start + partSize;
                    const partData = testData.slice(start, end);

                    showResult(`上传分块 ${partNumber}/${totalParts}...`, 'info');

                    const partResult = await provider.uploadPart(testKey, uploadId, partNumber, partData);

                    if (!partResult.success) {
                        throw new Error(`分块 ${partNumber} 上传失败: ${partResult.error.message}`);
                    }

                    parts.push(partResult.data);
                    showResult(`分块 ${partNumber} 上传成功`, 'success');
                }

                // 3. 完成分块上传
                showResult('完成分块上传...', 'info');
                const completeResult = await provider.completeMultipartUpload(testKey, uploadId, parts);

                if (!completeResult.success) {
                    throw new Error(`完成上传失败: ${completeResult.error.message}`);
                }
                showResult('分块上传完成', 'success');

                // 4. 验证结果
                const metadataResult = await provider.getMetadata(testKey);
                const sizeMatches = metadataResult.success && metadataResult.data.size >= totalSize;

                showResult(`文件大小验证: ${sizeMatches ? '通过' : '失败'}`, sizeMatches ? 'success' : 'warning');

                // 5. 清理
                await provider.delete(testKey);
                showResult('测试文件已清理', 'success');

                showResult('✅ 华为云OBS分块上传测试全部通过！', 'success');

            } catch (error) {
                showResult(`❌ 华为云OBS分块上传测试失败: ${error.message}`, 'error');
            }
        };

        // 同时测试两个Provider
        window.testBothProviders = async function() {
            showResult('开始综合测试两个Provider...', 'info');

            try {
                // 测试MinIO Provider
                showResult('=== 测试MinIO Provider ===', 'info');
                const minioProvider = new MinioProvider();
                await minioProvider.initialize(minioConfig);

                const minioTestKey = 'test/comprehensive-minio-test.txt';
                const minioTestData = `综合测试MinIO\n时间: ${new Date().toISOString()}`;

                const minioPutResult = await minioProvider.put(minioTestKey, minioTestData);
                if (!minioPutResult.success) {
                    throw new Error(`MinIO上传失败: ${minioPutResult.error.message}`);
                }
                showResult('MinIO上传成功', 'success');

                const minioGetResult = await minioProvider.get(minioTestKey);
                if (!minioGetResult.success) {
                    throw new Error(`MinIO下载失败: ${minioGetResult.error.message}`);
                }
                showResult('MinIO下载成功', 'success');

                await minioProvider.delete(minioTestKey);
                showResult('MinIO清理完成', 'success');

                // 测试华为云OBS Provider
                showResult('=== 测试华为云OBS Provider ===', 'info');
                const obsProvider = new HuaweiObsProvider();
                await obsProvider.initialize(obsConfig);

                const obsTestKey = 'test/comprehensive-obs-test.txt';
                const obsTestData = `综合测试华为云OBS\n时间: ${new Date().toISOString()}`;

                const obsPutResult = await obsProvider.put(obsTestKey, obsTestData);
                if (!obsPutResult.success) {
                    throw new Error(`华为云OBS上传失败: ${obsPutResult.error.message}`);
                }
                showResult('华为云OBS上传成功', 'success');

                const obsGetResult = await obsProvider.get(obsTestKey);
                if (!obsGetResult.success) {
                    throw new Error(`华为云OBS下载失败: ${obsGetResult.error.message}`);
                }
                showResult('华为云OBS下载成功', 'success');

                await obsProvider.delete(obsTestKey);
                showResult('华为云OBS清理完成', 'success');

                showResult('✅ 综合测试全部通过！两个Provider都工作正常！', 'success');

            } catch (error) {
                showResult(`❌ 综合测试失败: ${error.message}`, 'error');
            }
        };

        // 清除结果
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // 页面加载完成
        window.addEventListener('load', function() {
            showResult('页面加载完成，可以开始测试更新后的Provider', 'info');
            showResult('MinIO Provider现在使用AWS S3 SDK', 'info');
            showResult('华为云OBS Provider现在使用华为云OBS SDK', 'info');
        });
    </script>
</body>
</html>
