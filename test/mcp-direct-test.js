#!/usr/bin/env node

/**
 * MCP直接测试脚本
 * 尝试直接调用mcp-feedback-enhanced服务
 */

console.log('🧪 MCP直接测试开始...');
console.log('📋 目标服务: mcp-feedback-enhanced');

// 测试消息
const testMessage = {
    timestamp: new Date().toISOString(),
    message: '🔧 MCP服务直接测试',
    details: {
        testType: 'direct_call',
        service: 'mcp-feedback-enhanced',
        purpose: '验证MCP服务是否可以直接调用',
        environment: process.platform,
        nodeVersion: process.version
    }
};

console.log('📤 准备发送测试消息到MCP服务...');
console.log('📋 测试数据:', JSON.stringify(testMessage, null, 2));

// 模拟MCP调用
console.log('🎭 模拟MCP服务调用...');
console.log('⚠️  注意: 实际的MCP调用需要在支持MCP的环境中进行');

// 检查环境变量
console.log('\n🔍 检查MCP相关环境变量:');
const mcpEnvVars = Object.keys(process.env).filter(key => 
    key.toLowerCase().includes('mcp') || 
    key.toLowerCase().includes('feedback')
);

if (mcpEnvVars.length > 0) {
    console.log('✅ 发现MCP相关环境变量:');
    mcpEnvVars.forEach(key => {
        console.log(`  ${key}: ${process.env[key]}`);
    });
} else {
    console.log('❌ 未发现MCP相关环境变量');
}

// 检查是否有MCP相关的全局对象
console.log('\n🔍 检查全局MCP对象:');
if (typeof global !== 'undefined') {
    const globalKeys = Object.keys(global).filter(key => 
        key.toLowerCase().includes('mcp') || 
        key.toLowerCase().includes('feedback')
    );
    
    if (globalKeys.length > 0) {
        console.log('✅ 发现全局MCP对象:');
        globalKeys.forEach(key => {
            console.log(`  ${key}: ${typeof global[key]}`);
        });
    } else {
        console.log('❌ 未发现全局MCP对象');
    }
}

console.log('\n📊 测试结果:');
console.log('🎯 服务名称: mcp-feedback-enhanced ✅');
console.log('📝 测试消息: 已准备 ✅');
console.log('🔧 环境检查: 已完成 ✅');
console.log('⚠️  实际调用: 需要MCP环境支持');

console.log('\n💡 建议:');
console.log('1. 确认MCP服务在IDE中正确配置');
console.log('2. 检查MCP服务是否已启动');
console.log('3. 验证当前环境是否支持MCP调用');

console.log('\n🧪 MCP直接测试完成');
